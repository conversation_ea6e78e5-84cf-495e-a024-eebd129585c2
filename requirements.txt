# Excel文件合并工具 - 增强容灾版本依赖包

# 核心依赖
openpyxl>=3.0.0          # Excel文件读写
psutil>=5.8.0            # 系统资源监控

# 数值计算
decimal                  # 高精度数值计算 (Python内置)

# 系统和文件操作
pathlib                  # 路径操作 (Python 3.4+内置)
shutil                   # 文件操作 (Python内置)
os                       # 操作系统接口 (Python内置)
json                     # JSON数据处理 (Python内置)
configparser             # 配置文件解析 (Python内置)

# 并发和多进程
multiprocessing          # 多进程支持 (Python内置)
threading                # 多线程支持 (Python内置)

# 日志和调试
logging                  # 日志系统 (Python内置)
traceback                # 错误追踪 (Python内置)

# 时间和日期
datetime                 # 日期时间处理 (Python内置)
time                     # 时间操作 (Python内置)

# 类型注解
typing                   # 类型提示 (Python 3.5+内置)

# 上下文管理
contextlib               # 上下文管理工具 (Python内置)

# 信号处理
signal                   # 信号处理 (Python内置)
sys                      # 系统相关参数和函数 (Python内置)

# 垃圾回收
gc                       # 垃圾回收接口 (Python内置)

# 哈希计算
hashlib                  # 哈希算法 (Python内置)

# 注意：
# 1. 大部分依赖都是Python标准库，无需额外安装
# 2. 只有 openpyxl 和 psutil 需要通过pip安装
# 3. 安装命令：pip install openpyxl psutil
# 4. 或者：pip install -r requirements.txt
