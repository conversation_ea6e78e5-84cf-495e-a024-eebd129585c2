AS=as
CC="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe"
CCACHE_DIR=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
CCACHE_LOGFILE=C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
CCCOM=$CC -o $TARGET -c $CFLAGS $CCFLAGS $_CCCOMCOM $SOURCES
CFILESUFFIX=.c
CPPDEFINES=['_WIN32_WINNT=0x0501', '__NUITKA_NO_ASSERT__', 'MS_WIN64', '_NUITKA_CONSTANTS_FROM_RESOURCE', '_NUITKA_FROZEN=0', '_NUITKA_EXE', '_NUITKA_FILE_REFERENCE_ORIGINAL_MODE', '_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1']
CPPDEFPREFIX=-D
CPPDEFSUFFIX=
CPPPATH=['D:\\python\\Lib\\site-packages\\nuitka\\build\\inline_copy\\zlib', 'D:\\python\\include', '.', 'D:\\python\\Lib\\site-packages\\nuitka\\build\\include', 'D:\\python\\Lib\\site-packages\\nuitka\\build\\static_src', 'D:\\python\\Lib\\site-packages\\nuitka\\build\\inline_copy\\libbacktrace']
CPPSUFFIXES=['.c', '.C', '.cxx', '.cpp', '.c++', '.cc', '.h', '.H', '.hxx', '.hpp', '.hh', '.F', '.fpp', '.FPP', '.m', '.mm', '.S', '.spp', '.SPP', '.sx']
CXX="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe" "C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe"
CXXCOM=$CXX -o $TARGET -c $CXXFLAGS $CCFLAGS $_CCCOMCOM $SOURCES
CXXFILESUFFIX=.cc
HOST_ARCH=x86_64
HOST_OS=win32
INCPREFIX=-I
INCSUFFIX=
LDMODULE=$SHLINK
LDMODULEFLAGS=$SHLINKFLAGS
LDMODULENAME=${LDMODULEPREFIX}$_get_ldmodule_stem${_LDMODULESUFFIX}
LDMODULEPREFIX=$SHLIBPREFIX
LDMODULESUFFIX=$SHLIBSUFFIX
LDMODULEVERSION=$SHLIBVERSION
LDMODULE_NOVERSION_SYMLINK=$_get_shlib_dir${LDMODULEPREFIX}$_get_ldmodule_stem${LDMODULESUFFIX}
LDMODULE_SONAME_SYMLINK=$_get_shlib_dir$_LDMODULESONAME
LIBDIRPREFIX=-L
LIBDIRSUFFIX=
LIBLINKPREFIX=-l
LIBLINKSUFFIX=
LIBPATH=['D:\\python\\libs']
LIBPREFIX=lib
LIBPREFIXES=['$LIBPREFIX']
LIBS=['m', 'python311']
LIBSUFFIX=.a
LIBSUFFIXES=['$LIBSUFFIX']
LINK="C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe"
LINKCOM=$LINK -o $TARGET $LINKFLAGS $__RPATH @".\@link_input.txt" $_LIBDIRFLAGS $_LIBFLAGS
OBJPREFIX=
OBJSUFFIX=.o
PLATFORM=win32
PROGPREFIX=
PROGSUFFIX=.exe
RC=windres
RCCOM=$RC $_CPPDEFFLAGS $RCINCFLAGS ${RCINCPREFIX} ${SOURCE.dir} $RCFLAGS -i $SOURCE -o $TARGET
RCINCFLAGS=${_concat(RCINCPREFIX, CPPPATH, RCINCSUFFIX, __env__, RDirs, TARGET, SOURCE, affect_signature=False)}
RCINCPREFIX=--include-dir 
RCINCSUFFIX=
RPATHPREFIX=-Wl,-rpath=
RPATHSUFFIX=
SHCC=$CC
SHCCCOM=$SHCC -o $TARGET -c $SHCFLAGS $SHCCFLAGS $_CCCOMCOM $SOURCES
SHCXX=$CXX
SHCXXCOM=$SHCXX -o $TARGET -c $SHCXXFLAGS $SHCCFLAGS $_CCCOMCOM $SOURCES
SHELL=C:\Windows\System32\cmd.exe
SHLIBNAME=${_get_shlib_dir}${SHLIBPREFIX}$_get_shlib_stem${_SHLIBSUFFIX}
SHLIBPREFIX=
SHLIBSONAMEFLAGS=-Wl,-soname=$_SHLIBSONAME
SHLIBSUFFIX=.dll
SHLIB_NOVERSION_SYMLINK=${_get_shlib_dir}${SHLIBPREFIX}$_get_shlib_stem${SHLIBSUFFIX}
SHLIB_SONAME_SYMLINK=${_get_shlib_dir}$_SHLIBSONAME
SHLINK=$LINK
SHOBJPREFIX=$OBJPREFIX
SHOBJSUFFIX=.o
TARGET_ARCH=x86_64
TEMPFILEARGJOIN= 
TEMPFILEPREFIX=@
TOOLS=['mingw', 'gcc', 'g++', 'gnulink']
WINDOWSDEFPREFIX=
WINDOWSDEFSUFFIX=.def
gcc_mode=True
clang_mode=False
msvc_mode=False
mingw_mode=True
clangcl_mode=False
PATH=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin;C:\Python313\Scripts\;C:\Python313\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\windows\system32\HWAudioDriver\;D:\python\Scripts;D:\tailscale\;D:\PyCharm 2024.1.7\jbr\bin;D:\node\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\.vscode\extensions\ms-python.debugpy-2025.8.0-win32-x64\bundled\scripts\noConfigScripts
TARGET=C:\Users\<USER>\Desktop\py\hzhz.exe
