<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f89b23ac-0449-4752-8056-905a999bfd3b" name="变更" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
        <option value="SQL File" />
      </list>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="QODANA_PROBLEMS_VIEW_TAB" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2UmSDcRO4whSPXwkh06YlEfCA1b" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;MaxCompute SQL.1.sql.executor&quot;: &quot;Run&quot;,
    &quot;MaxCompute SQL.c.sql.executor&quot;: &quot;Run&quot;,
    &quot;MaxCompute SQL.建表.sql.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/新建文件夹 (2)/xxx&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="PyConsoleOptionsProvider">
    <option name="myPythonConsoleState">
      <console-settings module-name="py" is-module-sdk="true">
        <option name="myUseModuleSdk" value="true" />
        <option name="myModuleName" value="py" />
      </console-settings>
    </option>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\py\aa" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\py\aa" />
    </key>
  </component>
  <component name="RunManager" selected="MaxCompute SQL.c.sql">
    <configuration name="1.sql" type="OdpsQLRunnerConfigurationType" factoryName="MaxCompute SQL">
      <odps_target_file>$PROJECT_DIR$/1.sql</odps_target_file>
      <odps_output_dir>C:\Users\<USER>\AppData\Local\Temp\</odps_output_dir>
      <job_name>J_sql</job_name>
      <odps_parameter name="odps.compiler.runmode" value="Studio" />
      <method v="2" />
    </configuration>
    <configuration name="建表.sql" type="OdpsQLRunnerConfigurationType" factoryName="MaxCompute SQL">
      <odps_target_file>$PROJECT_DIR$/建表.sql</odps_target_file>
      <odps_output_dir>C:\Users\<USER>\AppData\Local\Temp\</odps_output_dir>
      <job_name>J__sql</job_name>
      <odps_parameter name="odps.compiler.runmode" value="Studio" />
      <method v="2" />
    </configuration>
    <configuration name="c.sql" type="OdpsQLRunnerConfigurationType" factoryName="MaxCompute SQL">
      <odps_target_file>$PROJECT_DIR$/c.sql</odps_target_file>
      <odps_output_dir>C:\Users\<USER>\AppData\Local\Temp\</odps_output_dir>
      <job_name>c_sql</job_name>
      <odps_parameter name="odps.compiler.runmode" value="Studio" />
      <method v="2" />
    </configuration>
    <configuration name="记录" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="py" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/aa" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/aa/记录.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="不分医院" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="py" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/不分医院.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="不分医院" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="py" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/不分医院.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="cf" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="py" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/cf.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="cs" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="py" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/cs.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="s (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="py" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/aa" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/aa/s.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="合并xlsx" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="py" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/合并xlsx.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.合并xlsx" />
        <item itemvalue="Python.cs" />
        <item itemvalue="Python.cf" />
        <item itemvalue="Python.s (1)" />
        <item itemvalue="Python.记录" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.19416.19" />
        <option value="bundled-python-sdk-337b0a7a993a-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.19416.19" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f89b23ac-0449-4752-8056-905a999bfd3b" name="变更" comment="" />
      <created>1693543148465</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1693543148465</updated>
      <workItem from="1697772639574" duration="1472000" />
      <workItem from="1698331207709" duration="166000" />
      <workItem from="1698331388558" duration="2499000" />
      <workItem from="1699446517526" duration="3234000" />
      <workItem from="1699513059087" duration="6588000" />
      <workItem from="1700062266145" duration="273000" />
      <workItem from="1715270041880" duration="2728000" />
      <workItem from="1715921521969" duration="4168000" />
      <workItem from="1716479787571" duration="207000" />
      <workItem from="1716529333203" duration="2653000" />
      <workItem from="1717130310128" duration="3103000" />
      <workItem from="1717662154373" duration="10194000" />
      <workItem from="1718099106990" duration="168000" />
      <workItem from="1718378287978" duration="1860000" />
      <workItem from="1718704575243" duration="851000" />
      <workItem from="1718937615358" duration="2813000" />
      <workItem from="1719222435999" duration="1136000" />
      <workItem from="1719375870610" duration="1018000" />
      <workItem from="1720599991023" duration="2558000" />
      <workItem from="1723046673563" duration="329000" />
      <workItem from="1723047013972" duration="311000" />
      <workItem from="1727104352303" duration="22000" />
      <workItem from="1727238110448" duration="3848000" />
      <workItem from="1727348401901" duration="148000" />
      <workItem from="1727663200140" duration="53000" />
      <workItem from="1731081064815" duration="31000" />
      <workItem from="1731081105478" duration="2000" />
      <workItem from="1731636854827" duration="689000" />
      <workItem from="1731655066287" duration="17000" />
      <workItem from="1744946147087" duration="1115000" />
      <workItem from="1744953284655" duration="236000" />
      <workItem from="1744953676893" duration="26000" />
      <workItem from="1744955029284" duration="322000" />
      <workItem from="1744955358921" duration="84000" />
      <workItem from="1744955491903" duration="65000" />
      <workItem from="1744955614278" duration="33000" />
      <workItem from="1744955676728" duration="472000" />
      <workItem from="1744956176392" duration="39000" />
      <workItem from="1744956300014" duration="18000" />
      <workItem from="1744957384678" duration="626000" />
      <workItem from="1744959699586" duration="47000" />
      <workItem from="1744960929657" duration="17000" />
      <workItem from="1745217367861" duration="2000" />
      <workItem from="1745282848792" duration="11000" />
      <workItem from="1745385080117" duration="996000" />
      <workItem from="1745386090014" duration="7610000" />
      <workItem from="1745543471840" duration="188000" />
      <workItem from="1745543833242" duration="28070000" />
      <workItem from="1745911486057" duration="3159000" />
      <workItem from="1745986147077" duration="11596000" />
      <workItem from="1746584395625" duration="16534000" />
      <workItem from="1746697116538" duration="303000" />
      <workItem from="1746761013506" duration="12840000" />
      <workItem from="1747014070822" duration="5299000" />
      <workItem from="1747037454399" duration="105000" />
      <workItem from="1747037567894" duration="2599000" />
      <workItem from="1747184308301" duration="4166000" />
      <workItem from="1747197035781" duration="298000" />
      <workItem from="1747212591443" duration="1203000" />
      <workItem from="1747272427791" duration="4006000" />
      <workItem from="1747293448874" duration="23131000" />
      <workItem from="1747638824328" duration="12222000" />
      <workItem from="1747707652890" duration="17041000" />
      <workItem from="1747728664135" duration="17365000" />
      <workItem from="1747811987510" duration="23815000" />
      <workItem from="1748231226935" duration="8892000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <watches-manager>
      <configuration name="PythonConfigurationType">
        <watch expression="folder_name" language="Python" />
      </configuration>
    </watches-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/py$xlsx.coverage" NAME="合并xlsx 覆盖结果" MODIFIED="1727348480770" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>