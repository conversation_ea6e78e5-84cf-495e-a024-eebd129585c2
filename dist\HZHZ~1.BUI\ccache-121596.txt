[2025-05-27T10:58:57.588319 128568] === CCACHE 4.8.2 STARTED =========================================
[2025-05-27T10:58:57.588395 130280] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-05-27T10:58:57.588405 128568] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-05-27T10:58:57.588461 128568] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-05-27T10:58:57.588436 130280] Config: (default) absolute_paths_in_stderr = false
[2025-05-27T10:58:57.588436 130280] Config: (default) base_dir = 
[2025-05-27T10:58:57.588436 130280] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-05-27T10:58:57.588436 130280] Config: (default) compiler = 
[2025-05-27T10:58:57.588436 130280] Config: (default) compiler_check = mtime
[2025-05-27T10:58:57.588436 130280] Config: (default) compiler_type = auto
[2025-05-27T10:58:57.588436 130280] Config: (default) compression = true
[2025-05-27T10:58:57.588436 130280] Config: (default) compression_level = 0
[2025-05-27T10:58:57.588436 130280] Config: (default) cpp_extension = 
[2025-05-27T10:58:57.588436 130280] Config: (default) debug = false
[2025-05-27T10:58:57.588436 130280] Config: (default) debug_dir = 
[2025-05-27T10:58:57.588436 130280] Config: (default) depend_mode = false
[2025-05-27T10:58:57.588436 130280] Config: (default) direct_mode = true
[2025-05-27T10:58:57.588436 130280] Config: (default) disable = false
[2025-05-27T10:58:57.588436 130280] Config: (default) extra_files_to_hash = 
[2025-05-27T10:58:57.588436 130280] Config: (default) file_clone = false
[2025-05-27T10:58:57.588436 130280] Config: (default) hard_link = false
[2025-05-27T10:58:57.588436 130280] Config: (default) hash_dir = true
[2025-05-27T10:58:57.588436 130280] Config: (default) ignore_headers_in_manifest = 
[2025-05-27T10:58:57.588436 130280] Config: (default) ignore_options = 
[2025-05-27T10:58:57.588436 130280] Config: (default) inode_cache = true
[2025-05-27T10:58:57.588436 130280] Config: (default) keep_comments_cpp = false
[2025-05-27T10:58:57.588436 130280] Config: (environment) log_file = C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
[2025-05-27T10:58:57.588436 130280] Config: (default) max_files = 0
[2025-05-27T10:58:57.588436 130280] Config: (default) max_size = 5.0 GiB
[2025-05-27T10:58:57.588436 130280] Config: (default) msvc_dep_prefix = Note: including file:
[2025-05-27T10:58:57.588436 130280] Config: (default) namespace = 
[2025-05-27T10:58:57.588436 130280] Config: (default) path = 
[2025-05-27T10:58:57.588436 130280] Config: (default) pch_external_checksum = false
[2025-05-27T10:58:57.588436 130280] Config: (default) prefix_command = 
[2025-05-27T10:58:57.588436 130280] Config: (default) prefix_command_cpp = 
[2025-05-27T10:58:57.588436 130280] Config: (default) read_only = false
[2025-05-27T10:58:57.588436 130280] Config: (default) read_only_direct = false
[2025-05-27T10:58:57.588436 130280] Config: (default) recache = false
[2025-05-27T10:58:57.588436 130280] Config: (default) remote_only = false
[2025-05-27T10:58:57.588436 130280] Config: (default) remote_storage = 
[2025-05-27T10:58:57.588436 130280] Config: (default) reshare = false
[2025-05-27T10:58:57.588436 130280] Config: (default) run_second_cpp = true
[2025-05-27T10:58:57.588436 130280] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-05-27T10:58:57.588436 130280] Config: (default) stats = true
[2025-05-27T10:58:57.588436 130280] Config: (default) stats_log = 
[2025-05-27T10:58:57.588436 130280] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.588436 130280] Config: (default) umask = 
[2025-05-27T10:58:57.588529 130280] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o __helpers.o -c -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABL[2025-05-27T10:58:57.588461 128568] Config: (default) absolute_paths_in_stderr = false
[2025-05-27T10:58:57.588461 128568] Config: (default) base_dir = 
[2025-05-27T10:58:57.588461 128568] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-05-27T10:58:57.588461 128568] Config: (default) compiler = 
[2025-05-27T10:58:57.588461 128568] Config: (default) compiler_check = mtime
[2025-05-27T10:58:57.588461 128568] Config: (default) compiler_type = auto
[2025-05-27T10:58:57.588461 128568] Config: (default) compression = true
[2025-05-27T10:58:57.588461 128568] Config: (default) compression_level = 0
[2025-05-27T10:58:57.588461 128568] Config: (default) cpp_extension = 
[2025-05-27T10:58:57.588461 128568] Config: (default) debug = false
[2025-05-27T10:58:57.588461 128568] Config: (default) debug_dir = 
[2025-05-27T10:58:57.588461 128568] Config: (default) depend_mode = false
[2025-05-27T10:58:57.588461 128568] Config: (default) direct_mode = true
[2025-05-27T10:58:57.588461 128568] Config: (default) disable = false
[2025-05-27T10:58:57.588461 128568] Config: (default) extra_files_to_hash = 
[2025-05-27T10:58:57.588461 128568] Config: (default) file_clone = false
[2025-05-27T10:58:57.588461 128568] Config: (default) hard_link = false
[2025-05-27T10:58:57.588461 128568] Config: (default) hash_dir = true
[2025-05-27T10:58:57.588461 128568] Config: (default) ignore_headers_in_manifest = 
[2025-05-27T10:58:57.588461 128568] Config: (default) ignore_options = 
[2025-05-27T10:58:57.588461 128568] Config: (default) inode_cache = true
[2025-05-27T10:58:57.588461 128568] Config: (default) keep_comments_cpp = false
[2025-05-27T10:58:57.588461 128568] Config: (environment) log_file = C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
[2025-05-27T10:58:57.588461 128568] Config: (default) max_files = 0
[2025-05-27T10:58:57.588461 128568] Config: (default) max_size = 5.0 GiB
[2025-05-27T10:58:57.588461 128568] Config: (default) msvc_dep_prefix = Note: including file:
[2025-05-27T10:58:57.588461 128568] Config: (default) namespace = 
[2025-05-27T10:58:57.588461 128568] Config: (default) path = 
[2025-05-27T10:58:57.588461 128568] Config: (default) pch_external_checksum = false
[2025-05-27T10:58:57.588461 128568] Config: (default) prefix_command = 
[2025-05-27T10:58:57.588461 128568] Config: (default) prefix_command_cpp = 
[2025-05-27T10:58:57.588461 128568] Config: (default) read_only = false
[2025-05-27T10:58:57.588461 128568] Config: (default) read_only_direct = false
[2025-05-27T10:58:57.588461 128568] Config: (default) recache = false
[2025-05-27T10:58:57.588461 128568] Config: (default) remote_only = false
[2025-05-27T10:58:57.588461 128568] Config: (default) remote_storage = 
[2025-05-27T10:58:57.588461 128568] Config: (default) reshare = false
[2025-05-27T10:58:57.588461 128568] Config: (default) run_second_cpp = true
[2025-05-27T10:58:57.588461 128568] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-05-27T10:58:57.588461 128568] Config: (default) stats = true
[2025-05-27T10:58:57.588461 128568] Config: (default) stats_log = 
[2025-05-27T10:58:57.588461 128568] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.588461 128568] Config: (default) umask = 
[2025-05-27T10:58:57.588545 128568] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o __constants.o -c -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace __constants.c
[2025-05-27T10:58:57.591967 84632] === CCACHE 4.8.2 STARTED =========================================
[2025-05-27T10:58:57.592013 84632] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-05-27T10:58:57.592019 84632] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-05-27T10:58:57.592019 84632] Config: (default) absolute_paths_in_stderr = false
[2025-05-27T10:58:57.592019 84632] Config: (default) base_dir = 
[2025-05-27T10:58:57.592019 84632] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-05-27T10:58:57.592019 84632] Config: (default) compiler = 
[2025-05-27T10:58:57.592019 84632] Config: (default) compiler_check = mtime
[2025-05-27T10:58:57.592019 84632] Config: (default) compiler_type = auto
[2025-05-27T10:58:57.592019 84632] Config: (default) compression = true
[2025-05-27T10:58:57.592019 84632] Config: (default) compression_level = 0
[2025-05-27T10:58:57.592019 84632] Config: (default) cpp_extension = 
[2025-05-27T10:58:57.592019 84632] Config: (default) debug = false
[2025-05-27T10:58:57.592019 84632] Config: (default) debug_dir = 
[2025-05-27T10:58:57.592019 84632] Config: (default) depend_mode = false
[2025-05-27T10:58:57.592019 84632] Config: (default) direct_mode = true
[2025-05-27T10:58:57.592019 84632] Config: (default) disable = false
[2025-05-27T10:58:57.592019 84632] Config: (default) extra_files_to_hash = 
[2025-05-27T10:58:57.592019 84632] Config: (default) file_clone = false
[2025-05-27T10:58:57.592019 84632] Config: (default) hard_link = false
[2025-05-27T10:58:57.592019 84632] Config: (default) hash_dir = true
[2025-05-27T10:58:57.592019 84632] Config: (default) ignore_headers_in_manifest = 
[2025-05-27T10:58:57.592019 84632] Config: (default) ignore_options = 
[2025-05-27T10:58:57.592019 84632] Config: (default) inode_cache = true
[2025-05-27T10:58:57.592019 84632] Config: (default) keep_comments_cpp = false
[2025-05-27T10:58:57.592019 84632] Config: (environment) log_file = C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
[2025-05-27T10:58:57.592019 84632] Config: (default) max_files = 0
[2025-05-27T10:58:57.592019 84632] Config: (default) max_size = 5.0 GiB
[2025-05-27T10:58:57.592019 84632] Config: (default) msvc_dep_prefix = Note: including file:
[2025-05-27T10:58:57.592019 84632] Config: (default) namespace = 
[2025-05-27T10:58:57.592019 84632] Config: (default) path = 
[2025-05-27T10:58:57.592019 84632] Config: (default) pch_external_checksum = false
[2025-05-27T10:58:57.592019 84632] Config: (default) prefix_command = 
[2025-05-27T10:58:57.592019 84632] Config: (default) prefix_command_cpp = 
[2025-05-27T10:58:57.592019 84632] Config: (default) read_only = false
[2025-05-27T10:58:57.592019 84632] Config: (default) read_only_direct = false
[2025-05-27T10:58:57.592019 84632] Config: (default) recache = false
[2025-05-27T10:58:57.592019 84632] Config: (default) remote_only = false
[2025-05-27T10:58:57.592019 84632] Config: (default) remote_storage = 
[2025-05-27T10:58:57.592019 84632] Config: (default) reshare = false
[2025-05-27T10:58:57.592019 84632] Config: (default) run_second_cpp = true
[2025-05-27T10:58:57.592019 84632] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-05-27T10:58:57.592019 84632] Config: (default) stats = true
[2025-05-27T10:58:57.592019 84632] Config: (default) stats_log = 
[2025-05-27T10:58:57.592019 84632] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.592019 84632] Config: (default) umask = 
[2025-05-27T10:58:57.592071 84632] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o __loader.o -c -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID[2025-05-27T10:58:57.592099 130280] Hostname: LAPTOP-IMPU3C28
Lib\site-packages\nuitka\build\inline_copy\libbacktrace __loader.c
[2025-05-27T10:58:57.592119 130280] Working directory: C:/Users/<USER>/Desktop/py/dist/HZHZ~1.BUI
NLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-05-27T10:58:57.592124 128568] Compiler type: gcc
[2025-05-27T10:58:57.592129 130280] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-05-27T10:58:57.592134 130280] Compiler type: gcc
[2025-05-27T10:58:57.592416 130280] Source file: __helpers.c

[2025-05-27T10:58:57.592421 128568] Object file: __constants.o
[2025-05-27T10:58:57.592423 130280] Object file: __helpers.o
[2025-05-27T10:58:57.592560 130280] Trying direct lookup
[2025-05-27T10:58:57.592639 130280] Manifest key: b58egc8omu5adrqebbun8hh4tjlcfe9k0
[2025-05-27T10:58:57.592670 128568] Manifest key: a7a3i4p4tnnqhar8vmqk7euqnrqpdj02i
[2025-05-27T10:58:57.592682 130280] No b58egc8omu5adrqebbun8hh4tjlcfe9k0 in local storage
[2025-05-27T10:58:57.592721 128568] No a7a3i4p4tnnqhar8vmqk7euqnrqpdj02i in local storage
[2025-05-27T10:58:57.593096 130280] Running preprocessor
[2025-05-27T10:58:57.593158 128568] Running preprocessor
[2025-05-27T10:58:57.593474 130280] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -E -o C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.QGHRcl.i __helpers.c
[2025-05-27T10:58:57.593538 128568] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -E -o C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.JO6FWx.i __constants.c
[2025-05-27T10:58:57.594489 84632] Hostname: LAPTOP-IMPU3C28
[2025-05-27T10:58:57.594505 84632] Working directory: C:/Users/<USER>/Desktop/py/dist/HZHZ~1.BUI
[2025-05-27T10:58:57.594516 84632] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-05-27T10:58:57.594520 84632] Compiler type: gcc
[2025-05-27T10:58:57.594739 84632] Source file: __loader.c
[2025-05-27T10:58:57.594745 84632] Object file: __loader.o
[2025-05-27T10:58:57.594835 84632] Trying direct lookup
[2025-05-27T10:58:57.594914 84632] Manifest key: c914fcstj4q55hl2u1oqetl5nru639ima
[2025-05-27T10:58:57.594953 84632] No c914fcstj4q55hl2u1oqetl5nru639ima in local storage
[2025-05-27T10:58:57.595264 84632] Running preprocessor
[2025-05-27T10:58:57.595352 84632] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -E -o C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.N8IZRe.i __loader.c
[2025-05-27T10:58:57.621280 68732] === CCACHE 4.8.2 STARTED =========================================
[2025-05-27T10:58:57.621312 68732] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-05-27T10:58:57.621319 68732] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-05-27T10:58:57.621319 68732] Config: (default) absolute_paths_in_stderr = false
[2025-05-27T10:58:57.621319 68732] Config: (default) base_dir = 
[2025-05-27T10:58:57.621319 68732] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-05-27T10:58:57.621319 68732] Config: (default) compiler = 
[2025-05-27T10:58:57.621319 68732] Config: (default) compiler_check = mtime
[2025-05-27T10:58:57.621319 68732] Config: (default) compiler_type = auto
[2025-05-27T10:58:57.621319 68732] Config: (default) compression = true
[2025-05-27T10:58:57.621319 68732] Config: (default) compression_level = 0
[2025-05-27T10:58:57.621319 68732] Config: (default) cpp_extension = 
[2025-05-27T10:58:57.621319 68732] Config: (default) debug = false
[2025-05-27T10:58:57.621319 68732] Config: (default) debug_dir = 
[2025-05-27T10:58:57.621319 68732] Config: (default) depend_mode = false
[2025-05-27T10:58:57.621319 68732] Config: (default) direct_mode = true
[2025-05-27T10:58:57.621319 68732] Config: (default) disable = false
[2025-05-27T10:58:57.621319 68732] Config: (default) extra_files_to_hash = 
[2025-05-27T10:58:57.621319 68732] Config: (default) file_clone = false
[2025-05-27T10:58:57.621319 68732] Config: (default) hard_link = false
[2025-05-27T10:58:57.621319 68732] Config: (default) hash_dir = true
[2025-05-27T10:58:57.621319 68732] Config: (default) ignore_headers_in_manifest = 
[2025-05-27T10:58:57.621319 68732] Config: (default) ignore_options = 
[2025-05-27T10:58:57.621319 68732] Config: (default) inode_cache = true
[2025-05-27T10:58:57.621319 68732] Config: (default) keep_comments_cpp = false
[2025-05-27T10:58:57.621319 68732] Config: (environment) log_file = C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
[2025-05-27T10:58:57.621319 68732] Config: (default) max_files = 0
[2025-05-27T10:58:57.621319 68732] Config: (default) max_size = 5.0 GiB
[2025-05-27T10:58:57.621319 68732] Config: (default) msvc_dep_prefix = Note: including file:
[2025-05-27T10:58:57.621319 68732] Config: (default) namespace = 
[2025-05-27T10:58:57.621319 68732] Config: (default) path = 
[2025-05-27T10:58:57.621319 68732] Config: (default) pch_external_checksum = false
[2025-05-27T10:58:57.621319 68732] Config: (default) prefix_command = 
[2025-05-27T10:58:57.621319 68732] Config: (default) prefix_command_cpp = 
[2025-05-27T10:58:57.621319 68732] Config: (default) read_only = false
[2025-05-27T10:58:57.621319 68732] Config: (default) read_only_direct = false
[2025-05-27T10:58:57.621319 68732] Config: (default) recache = false
[2025-05-27T10:58:57.621319 68732] Config: (default) remote_only = false
[2025-05-27T10:58:57.621319 68732] Config: (default) remote_storage = 
[2025-05-27T10:58:57.621319 68732] Config: (default) reshare = false
[2025-05-27T10:58:57.621319 68732] Config: (default) run_second_cpp = true
[2025-05-27T10:58:57.621319 68732] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-05-27T10:58:57.621319 68732] Config: (default) stats = true
[2025-05-27T10:58:57.621319 68732] Config: (default) stats_log = 
[2025-05-27T10:58:57.621319 68732] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.621319 68732] Config: (default) umask = 
[2025-05-27T10:58:57.621361 68732] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o module.__main__.o -c -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace module.__main__.c
[2025-05-27T10:58:57.623711 68732] Hostname: LAPTOP-IMPU3C28
[2025-05-27T10:58:57.623725 68732] Working directory: C:/Users/<USER>/Desktop/py/dist/HZHZ~1.BUI
[2025-05-27T10:58:57.623734 68732] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-05-27T10:58:57.623739 68732] Compiler type: gcc
[2025-05-27T10:58:57.623948 68732] Source file: module.__main__.c
[2025-05-27T10:58:57.623955 68732] Object file: module.__main__.o
[2025-05-27T10:58:57.624044 68732] Trying direct lookup
[2025-05-27T10:58:57.626763 68732] Manifest key: d74ci21q29ee2c8u0mpt27veqn8nebd38
[2025-05-27T10:58:57.626811 68732] No d74ci21q29ee2c8u0mpt27veqn8nebd38 in local storage
[2025-05-27T10:58:57.627326 68732] Running preprocessor
[2025-05-27T10:58:57.627437 68732] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -E -o C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.090JHk.i module.__main__.c
[2025-05-27T10:58:57.661358 130688] === CCACHE 4.8.2 STARTED =========================================
[2025-05-27T10:58:57.661408 130688] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-05-27T10:58:57.661416 130688] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-05-27T10:58:57.661416 130688] Config: (default) absolute_paths_in_stderr = false
[2025-05-27T10:58:57.661416 130688] Config: (default) base_dir = 
[2025-05-27T10:58:57.661416 130688] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-05-27T10:58:57.661416 130688] Config: (default) compiler = 
[2025-05-27T10:58:57.661416 130688] Config: (default) compiler_check = mtime
[2025-05-27T10:58:57.661416 130688] Config: (default) compiler_type = auto
[2025-05-27T10:58:57.661416 130688] Config: (default) compression = true
[2025-05-27T10:58:57.661416 130688] Config: (default) compression_level = 0
[2025-05-27T10:58:57.661416 130688] Config: (default) cpp_extension = 
[2025-05-27T10:58:57.661416 130688] Config: (default) debug = false
[2025-05-27T10:58:57.661416 130688] Config: (default) debug_dir = 
[2025-05-27T10:58:57.661416 130688] Config: (default) depend_mode = false
[2025-05-27T10:58:57.661416 130688] Config: (default) direct_mode = true
[2025-05-27T10:58:57.661416 130688] Config: (default) disable = false
[2025-05-27T10:58:57.661416 130688] Config: (default) extra_files_to_hash = 
[2025-05-27T10:58:57.661416 130688] Config: (default) file_clone = false
[2025-05-27T10:58:57.661416 130688] Config: (default) hard_link = false
[2025-05-27T10:58:57.661416 130688] Config: (default) hash_dir = true
[2025-05-27T10:58:57.661416 130688] Config: (default) ignore_headers_in_manifest = 
[2025-05-27T10:58:57.661416 130688] Config: (default) ignore_options = 
[2025-05-27T10:58:57.661416 130688] Config: (default) inode_cache = true
[2025-05-27T10:58:57.661416 130688] Config: (default) keep_comments_cpp = false
[2025-05-27T10:58:57.661416 130688] Config: (environment) log_file = C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
[2025-05-27T10:58:57.661416 130688] Config: (default) max_files = 0
[2025-05-27T10:58:57.661416 130688] Config: (default) max_size = 5.0 GiB
[2025-05-27T10:58:57.661416 130688] Config: (default) msvc_dep_prefix = Note: including file:
[2025-05-27T10:58:57.661416 130688] Config: (default) namespace = 
[2025-05-27T10:58:57.661416 130688] Config: (default) path = 
[2025-05-27T10:58:57.661416 130688] Config: (default) pch_external_checksum = false
[2025-05-27T10:58:57.661416 130688] Config: (default) prefix_command = 
[2025-05-27T10:58:57.661416 130688] Config: (default) prefix_command_cpp = 
[2025-05-27T10:58:57.661416 130688] Config: (default) read_only = false
[2025-05-27T10:58:57.661416 130688] Config: (default) read_only_direct = false
[2025-05-27T10:58:57.661416 130688] Config: (default) recache = false
[2025-05-27T10:58:57.661416 130688] Config: (default) remote_only = false
[2025-05-27T10:58:57.661416 130688] Config: (default) remote_storage = 
[2025-05-27T10:58:57.661416 130688] Config: (default) reshare = false
[2025-05-27T10:58:57.661416 130688] Config: (default) run_second_cpp = true
[2025-05-27T10:58:57.661416 130688] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-05-27T10:58:57.661416 130688] Config: (default) stats = true
[2025-05-27T10:58:57.661416 130688] Config: (default) stats_log = 
[2025-05-27T10:58:57.661416 130688] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.661416 130688] Config: (default) umask = 
[2025-05-27T10:58:57.661479 130688] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o module.__parents_main__.o -c -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace module.__parents_main__.c
[2025-05-27T10:58:57.665138 130688] Hostname: LAPTOP-IMPU3C28
[2025-05-27T10:58:57.665157 130688] Working directory: C:/Users/<USER>/Desktop/py/dist/HZHZ~1.BUI
[2025-05-27T10:58:57.665170 130688] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-05-27T10:58:57.665177 130688] Compiler type: gcc
[2025-05-27T10:58:57.665510 130688] Source file: module.__parents_main__.c
[2025-05-27T10:58:57.665538 130688] Object file: module.__parents_main__.o
[2025-05-27T10:58:57.665691 130688] Trying direct lookup
[2025-05-27T10:58:57.669416 49360] === CCACHE 4.8.2 STARTED =========================================
[2025-05-27T10:58:57.669452 49360] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-05-27T10:58:57.669459 49360] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-05-27T10:58:57.669459 49360] Config: (default) absolute_paths_in_stderr = false
[2025-05-27T10:58:57.669459 49360] Config: (default) base_dir = 
[2025-05-27T10:58:57.669459 49360] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-05-27T10:58:57.669459 49360] Config: (default) compiler = 
[2025-05-27T10:58:57.669459 49360] Config: (default) compiler_check = mtime
[2025-05-27T10:58:57.669459 49360] Config: (default) compiler_type = auto
[2025-05-27T10:58:57.669459 49360] Config: (default) compression = true
[2025-05-27T10:58:57.669459 49360] Config: (default) compression_level = 0
[2025-05-27T10:58:57.669459 49360] Config: (default) cpp_extension = 
[2025-05-27T10:58:57.669459 49360] Config: (default) debug = false
[2025-05-27T10:58:57.669459 49360] Config: (default) debug_dir = 
[2025-05-27T10:58:57.669459 49360] Config: (default) depend_mode = false
[2025-05-27T10:58:57.669459 49360] Config: (default) direct_mode = true
[2025-05-27T10:58:57.669459 49360] Config: (default) disable = false
[2025-05-27T10:58:57.669459 49360] Config: (default) extra_files_to_hash = 
[2025-05-27T10:58:57.669459 49360] Config: (default) file_clone = false
[2025-05-27T10:58:57.669459 49360] Config: (default) hard_link = false
[2025-05-27T10:58:57.669459 49360] Config: (default) hash_dir = true
[2025-05-27T10:58:57.669459 49360] Config: (default) ignore_headers_in_manifest = 
[2025-05-27T10:58:57.669459 49360] Config: (default) ignore_options = 
[2025-05-27T10:58:57.669459 49360] Config: (default) inode_cache = true
[2025-05-27T10:58:57.669459 49360] Config: (default) keep_comments_cpp = false
[2025-05-27T10:58:57.669459 49360] Config: (environment) log_file = C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
[2025-05-27T10:58:57.669459 49360] Config: (default) max_files = 0
[2025-05-27T10:58:57.669459 49360] Config: (default) max_size = 5.0 GiB
[2025-05-27T10:58:57.669459 49360] Config: (default) msvc_dep_prefix = Note: including file:
[2025-05-27T10:58:57.669459 49360] Config: (default) namespace = 
[2025-05-27T10:58:57.669459 49360] Config: (default) path = 
[2025-05-27T10:58:57.669459 49360] Config: (default) pch_external_checksum = false
[2025-05-27T10:58:57.669459 49360] Config: (default) prefix_command = 
[2025-05-27T10:58:57.669459 49360] Config: (default) prefix_command_cpp = 
[2025-05-27T10:58:57.669459 49360] Config: (default) read_only = false
[2025-05-27T10:58:57.669459 49360] Config: (default) read_only_direct = false
[2025-05-27T10:58:57.669459 49360] Config: (default) recache = false
[2025-05-27T10:58:57.669459 49360] Config: (default) remote_only = false
[2025-05-27T10:58:57.669459 49360] Config: (default) remote_storage = 
[2025-05-27T10:58:57.669459 49360] Config: (default) reshare = false
[2025-05-27T10:58:57.669459 49360] Config: (default) run_second_cpp = true
[2025-05-27T10:58:57.669459 49360] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-05-27T10:58:57.669459 49360] Config: (default) stats = true
[2025-05-27T10:58:57.669459 49360] Config: (default) stats_log = 
[2025-05-27T10:58:57.669459 49360] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.669459 49360] Config: (default) umask = 
[2025-05-27T10:58:57.669500 49360] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o module.multiprocessing-postLoad.o -c -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace module.multiprocessing-postLoad.c
[2025-05-27T10:58:57.669904 130688] Manifest key: 7126qr453k41pahutajddiles2i4i1934
[2025-05-27T10:58:57.669971 130688] No 7126qr453k41pahutajddiles2i4i1934 in local storage
[2025-05-27T10:58:57.670390 130688] Running preprocessor
[2025-05-27T10:58:57.670536 130688] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -E -o C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.lDfMSN.i module.__parents_main__.c
[2025-05-27T10:58:57.671006 90828] === CCACHE 4.8.2 STARTED =========================================
[2025-05-27T10:58:57.671036 90828] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-05-27T10:58:57.671042 90828] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-05-27T10:58:57.671042 90828] Config: (default) absolute_paths_in_stderr = false
[2025-05-27T10:58:57.671042 90828] Config: (default) base_dir = 
[2025-05-27T10:58:57.671042 90828] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-05-27T10:58:57.671042 90828] Config: (default) compiler = 
[2025-05-27T10:58:57.671042 90828] Config: (default) compiler_check = mtime
[2025-05-27T10:58:57.671042 90828] Config: (default) compiler_type = auto
[2025-05-27T10:58:57.671042 90828] Config: (default) compression = true
[2025-05-27T10:58:57.671042 90828] Config: (default) compression_level = 0
[2025-05-27T10:58:57.671042 90828] Config: (default) cpp_extension = 
[2025-05-27T10:58:57.671042 90828] Config: (default) debug = false
[2025-05-27T10:58:57.671042 90828] Config: (default) debug_dir = 
[2025-05-27T10:58:57.671042 90828] Config: (default) depend_mode = false
[2025-05-27T10:58:57.671042 90828] Config: (default) direct_mode = true
[2025-05-27T10:58:57.671042 90828] Config: (default) disable = false
[2025-05-27T10:58:57.671042 90828] Config: (default) extra_files_to_hash = 
[2025-05-27T10:58:57.671042 90828] Config: (default) file_clone = false
[2025-05-27T10:58:57.671042 90828] Config: (default) hard_link = false
[2025-05-27T10:58:57.671042 90828] Config: (default) hash_dir = true
[2025-05-27T10:58:57.671042 90828] Config: (default) ignore_headers_in_manifest = 
[2025-05-27T10:58:57.671042 90828] Config: (default) ignore_options = 
[2025-05-27T10:58:57.671042 90828] Config: (default) inode_cache = true
[2025-05-27T10:58:57.671042 90828] Config: (default) keep_comments_cpp = false
[2025-05-27T10:58:57.671042 90828] Config: (environment) log_file = C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
[2025-05-27T10:58:57.671042 90828] Config: (default) max_files = 0
[2025-05-27T10:58:57.671042 90828] Config: (default) max_size = 5.0 GiB
[2025-05-27T10:58:57.671042 90828] Config: (default) msvc_dep_prefix = Note: including file:
[2025-05-27T10:58:57.671042 90828] Config: (default) namespace = 
[2025-05-27T10:58:57.671042 90828] Config: (default) path = 
[2025-05-27T10:58:57.671042 90828] Config: (default) pch_external_checksum = false
[2025-05-27T10:58:57.671042 90828] Config: (default) prefix_command = 
[2025-05-27T10:58:57.671042 90828] Config: (default) prefix_command_cpp = 
[2025-05-27T10:58:57.671042 90828] Config: (default) read_only = false
[2025-05-27T10:58:57.671042 90828] Config: (default) read_only_direct = false
[2025-05-27T10:58:57.671042 90828] Config: (default) recache = false
[2025-05-27T10:58:57.671042 90828] Config: (default) remote_only = false
[2025-05-27T10:58:57.671042 90828] Config: (default) remote_storage = 
[2025-05-27T10:58:57.671042 90828] Config: (default) reshare = false
[2025-05-27T10:58:57.671042 90828] Config: (default) run_second_cpp = true
[2025-05-27T10:58:57.671042 90828] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-05-27T10:58:57.671042 90828] Config: (default) stats = true
[2025-05-27T10:58:57.671042 90828] Config: (default) stats_log = 
[2025-05-27T10:58:57.671042 90828] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.671042 90828] Config: (default) umask = 
[2025-05-27T10:58:57.671083 90828] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o module.multiprocessing-preLoad.o -c -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace module.multiprocessing-preLoad.c
[2025-05-27T10:58:57.673144 49360] Hostname: LAPTOP-IMPU3C28
[2025-05-27T10:58:57.673170 49360] Working directory: C:/Users/<USER>/Desktop/py/dist/HZHZ~1.BUI
[2025-05-27T10:58:57.673186 49360] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-05-27T10:58:57.673195 49360] Compiler type: gcc
[2025-05-27T10:58:57.673493 90828] Hostname: LAPTOP-IMPU3C28
[2025-05-27T10:58:57.673507 90828] Working directory: C:/Users/<USER>/Desktop/py/dist/HZHZ~1.BUI
[2025-05-27T10:58:57.673514 90828] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-05-27T10:58:57.673519 90828] Compiler type: gcc
[2025-05-27T10:58:57.673618 49360] Source file: module.multiprocessing-postLoad.c
[2025-05-27T10:58:57.673628 49360] Object file: module.multiprocessing-postLoad.o
[2025-05-27T10:58:57.673731 49360] Trying direct lookup
[2025-05-27T10:58:57.673802 90828] Source file: module.multiprocessing-preLoad.c
[2025-05-27T10:58:57.673814 90828] Object file: module.multiprocessing-preLoad.o
[2025-05-27T10:58:57.673916 49360] Manifest key: 481afo4egnles6hfpveobv5349fb48vko
[2025-05-27T10:58:57.673930 90828] Trying direct lookup
[2025-05-27T10:58:57.673969 49360] No 481afo4egnles6hfpveobv5349fb48vko in local storage
[2025-05-27T10:58:57.674052 90828] Manifest key: 8d419lql6d7svc0q8go1dnefbh0g8p65i
[2025-05-27T10:58:57.674107 90828] No 8d419lql6d7svc0q8go1dnefbh0g8p65i in local storage
[2025-05-27T10:58:57.674477 49360] Running preprocessor
[2025-05-27T10:58:57.674549 90828] Running preprocessor
[2025-05-27T10:58:57.674662 90828] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -E -o C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.tvBPUF.i module.multiprocessing-preLoad.c
[2025-05-27T10:58:57.674796 49360] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -E -o C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.BYPb3O.i module.multiprocessing-postLoad.c
[2025-05-27T10:58:57.681623 123720] === CCACHE 4.8.2 STARTED =========================================
[2025-05-27T10:58:57.681667 123720] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-05-27T10:58:57.681676 123720] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-05-27T10:58:57.681676 123720] Config: (default) absolute_paths_in_stderr = false
[2025-05-27T10:58:57.681676 123720] Config: (default) base_dir = 
[2025-05-27T10:58:57.681676 123720] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-05-27T10:58:57.681676 123720] Config: (default) compiler = 
[2025-05-27T10:58:57.681676 123720] Config: (default) compiler_check = mtime
[2025-05-27T10:58:57.681676 123720] Config: (default) compiler_type = auto
[2025-05-27T10:58:57.681676 123720] Config: (default) compression = true
[2025-05-27T10:58:57.681676 123720] Config: (default) compression_level = 0
[2025-05-27T10:58:57.681676 123720] Config: (default) cpp_extension = 
[2025-05-27T10:58:57.681676 123720] Config: (default) debug = false
[2025-05-27T10:58:57.681676 123720] Config: (default) debug_dir = 
[2025-05-27T10:58:57.681676 123720] Config: (default) depend_mode = false
[2025-05-27T10:58:57.681676 123720] Config: (default) direct_mode = true
[2025-05-27T10:58:57.681676 123720] Config: (default) disable = false
[2025-05-27T10:58:57.681676 123720] Config: (default) extra_files_to_hash = 
[2025-05-27T10:58:57.681676 123720] Config: (default) file_clone = false
[2025-05-27T10:58:57.681676 123720] Config: (default) hard_link = false
[2025-05-27T10:58:57.681676 123720] Config: (default) hash_dir = true
[2025-05-27T10:58:57.681676 123720] Config: (default) ignore_headers_in_manifest = 
[2025-05-27T10:58:57.681676 123720] Config: (default) ignore_options = 
[2025-05-27T10:58:57.681676 123720] Config: (default) inode_cache = true
[2025-05-27T10:58:57.681676 123720] Config: (default) keep_comments_cpp = false
[2025-05-27T10:58:57.681676 123720] Config: (environment) log_file = C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
[2025-05-27T10:58:57.681676 123720] Config: (default) max_files = 0
[2025-05-27T10:58:57.681676 123720] Config: (default) max_size = 5.0 GiB
[2025-05-27T10:58:57.681676 123720] Config: (default) msvc_dep_prefix = Note: including file:
[2025-05-27T10:58:57.681676 123720] Config: (default) namespace = 
[2025-05-27T10:58:57.681676 123720] Config: (default) path = 
[2025-05-27T10:58:57.681676 123720] Config: (default) pch_external_checksum = false
[2025-05-27T10:58:57.681676 123720] Config: (default) prefix_command = 
[2025-05-27T10:58:57.681676 123720] Config: (default) prefix_command_cpp = 
[2025-05-27T10:58:57.681676 123720] Config: (default) read_only = false
[2025-05-27T10:58:57.681676 123720] Config: (default) read_only_direct = false
[2025-05-27T10:58:57.681676 123720] Config: (default) recache = false
[2025-05-27T10:58:57.681676 123720] Config: (default) remote_only = false
[2025-05-27T10:58:57.681676 123720] Config: (default) remote_storage = 
[2025-05-27T10:58:57.681676 123720] Config: (default) reshare = false
[2025-05-27T10:58:57.681676 123720] Config: (default) run_second_cpp = true
[2025-05-27T10:58:57.681676 123720] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-05-27T10:58:57.681676 123720] Config: (default) stats = true
[2025-05-27T10:58:57.681676 123720] Config: (default) stats_log = 
[2025-05-27T10:58:57.681676 123720] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.681676 123720] Config: (default) umask = 
[2025-05-27T10:58:57.681739 123720] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o static_src\MainProgram.o -c -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace static_src\MainProgram.c
[2025-05-27T10:58:57.683452 84632] Got result key from preprocessor
[2025-05-27T10:58:57.683468 84632] Result key: e16e94csrbf33m10d7n6smds1vhug5oi4
[2025-05-27T10:58:57.683504 84632] No e16e94csrbf33m10d7n6smds1vhug5oi4 in local storage
[2025-05-27T10:58:57.683519 84632] Running real compiler
[2025-05-27T10:58:57.683919 84632] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -c -fdiagnostics-color -o __loader.o __loader.c
[2025-05-27T10:58:57.685204 123720] Hostname: LAPTOP-IMPU3C28
[2025-05-27T10:58:57.685218 123720] Working directory: C:/Users/<USER>/Desktop/py/dist/HZHZ~1.BUI
[2025-05-27T10:58:57.685228 123720] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-05-27T10:58:57.685233 123720] Compiler type: gcc
[2025-05-27T10:58:57.685469 123720] Source file: static_src\MainProgram.c
[2025-05-27T10:58:57.685476 123720] Object file: static_src\MainProgram.o
[2025-05-27T10:58:57.685556 123720] Trying direct lookup
[2025-05-27T10:58:57.685684 123720] Manifest key: 282cks62gbm2iscelc4djloigro07hfbc
[2025-05-27T10:58:57.685722 123720] No 282cks62gbm2iscelc4djloigro07hfbc in local storage
[2025-05-27T10:58:57.686060 123720] Running preprocessor
[2025-05-27T10:58:57.686377 123720] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -E -o C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.DQVlO2.i static_src\MainProgram.c
[2025-05-27T10:58:57.782111 84632] Using default compression level 1
[2025-05-27T10:58:57.782157 84632] Storing embedded entry #0 .o (15665 bytes) from __loader.o
[2025-05-27T10:58:57.783013 84632] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e1.lock
[2025-05-27T10:58:57.783134 84632] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e1.lock
[2025-05-27T10:58:57.783311 84632] Stored e16e94csrbf33m10d7n6smds1vhug5oi4 in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/1/6e94csrbf33m10d7n6smds1vhug5oi4R)
[2025-05-27T10:58:57.783346 84632] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:58:57.783428 84632] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:58:57.783668 84632] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:58:57.783735 84632] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:58:57.783749 84632] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e1.lock
[2025-05-27T10:58:57.783782 84632] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e1.lock
[2025-05-27T10:58:57.785869 84632] Added result key to manifest c914fcstj4q55hl2u1oqetl5nru639ima
[2025-05-27T10:58:57.785882 84632] Using default compression level 1
[2025-05-27T10:58:57.786689 84632] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_c9.lock
[2025-05-27T10:58:57.786752 84632] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_c9.lock
[2025-05-27T10:58:57.786884 84632] Stored c914fcstj4q55hl2u1oqetl5nru639ima in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/9/14fcstj4q55hl2u1oqetl5nru639imaM)
[2025-05-27T10:58:57.786910 84632] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-05-27T10:58:57.786963 84632] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-05-27T10:58:57.787504 84632] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-05-27T10:58:57.787597 84632] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/stats.lock
[2025-05-27T10:58:57.787609 84632] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_c9.lock
[2025-05-27T10:58:57.787670 84632] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_c9.lock
[2025-05-27T10:58:57.787960 84632] Result: cache_miss
[2025-05-27T10:58:57.787975 84632] Result: direct_cache_miss
[2025-05-27T10:58:57.787982 84632] Result: local_storage_miss
[2025-05-27T10:58:57.787989 84632] Result: local_storage_read_miss
[2025-05-27T10:58:57.787995 84632] Result: local_storage_read_miss
[2025-05-27T10:58:57.788002 84632] Result: local_storage_write
[2025-05-27T10:58:57.788008 84632] Result: local_storage_write
[2025-05-27T10:58:57.788014 84632] Result: preprocessed_cache_miss
[2025-05-27T10:58:57.788023 84632] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/9/8/stats.lock
[2025-05-27T10:58:57.788536 84632] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/9/8/stats.lock
[2025-05-27T10:58:57.788971 84632] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/9/8/stats.lock
[2025-05-27T10:58:57.789058 84632] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/9/8/stats.lock
[2025-05-27T10:58:57.789143 84632] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:57.789259 84632] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:57.789639 84632] No automatic cleanup needed (size 24.0 KiB, files 2, max size 5.0 GiB)
[2025-05-27T10:58:57.789654 84632] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:57.789721 84632] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:57.789752 84632] Cleaning up C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.791284 84632] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.SsHtp3.tmp
[2025-05-27T10:58:57.791381 84632] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.JEDKst.tmp
[2025-05-27T10:58:57.791471 84632] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.bcML20.tmp
[2025-05-27T10:58:57.791635 84632] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.N8IZRe.i
[2025-05-27T10:58:57.852556 30408] === CCACHE 4.8.2 STARTED =========================================
[2025-05-27T10:58:57.852605 30408] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-05-27T10:58:57.852615 30408] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-05-27T10:58:57.852615 30408] Config: (default) absolute_paths_in_stderr = false
[2025-05-27T10:58:57.852615 30408] Config: (default) base_dir = 
[2025-05-27T10:58:57.852615 30408] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-05-27T10:58:57.852615 30408] Config: (default) compiler = 
[2025-05-27T10:58:57.852615 30408] Config: (default) compiler_check = mtime
[2025-05-27T10:58:57.852615 30408] Config: (default) compiler_type = auto
[2025-05-27T10:58:57.852615 30408] Config: (default) compression = true
[2025-05-27T10:58:57.852615 30408] Config: (default) compression_level = 0
[2025-05-27T10:58:57.852615 30408] Config: (default) cpp_extension = 
[2025-05-27T10:58:57.852615 30408] Config: (default) debug = false
[2025-05-27T10:58:57.852615 30408] Config: (default) debug_dir = 
[2025-05-27T10:58:57.852615 30408] Config: (default) depend_mode = false
[2025-05-27T10:58:57.852615 30408] Config: (default) direct_mode = true
[2025-05-27T10:58:57.852615 30408] Config: (default) disable = false
[2025-05-27T10:58:57.852615 30408] Config: (default) extra_files_to_hash = 
[2025-05-27T10:58:57.852615 30408] Config: (default) file_clone = false
[2025-05-27T10:58:57.852615 30408] Config: (default) hard_link = false
[2025-05-27T10:58:57.852615 30408] Config: (default) hash_dir = true
[2025-05-27T10:58:57.852615 30408] Config: (default) ignore_headers_in_manifest = 
[2025-05-27T10:58:57.852615 30408] Config: (default) ignore_options = 
[2025-05-27T10:58:57.852615 30408] Config: (default) inode_cache = true
[2025-05-27T10:58:57.852615 30408] Config: (default) keep_comments_cpp = false
[2025-05-27T10:58:57.852615 30408] Config: (environment) log_file = C:\Users\<USER>\Desktop\py\dist\HZHZ~1.BUI\ccache-121596.txt
[2025-05-27T10:58:57.852615 30408] Config: (default) max_files = 0
[2025-05-27T10:58:57.852615 30408] Config: (default) max_size = 5.0 GiB
[2025-05-27T10:58:57.852615 30408] Config: (default) msvc_dep_prefix = Note: including file:
[2025-05-27T10:58:57.852615 30408] Config: (default) namespace = 
[2025-05-27T10:58:57.852615 30408] Config: (default) path = 
[2025-05-27T10:58:57.852615 30408] Config: (default) pch_external_checksum = false
[2025-05-27T10:58:57.852615 30408] Config: (default) prefix_command = 
[2025-05-27T10:58:57.852615 30408] Config: (default) prefix_command_cpp = 
[2025-05-27T10:58:57.852615 30408] Config: (default) read_only = false
[2025-05-27T10:58:57.852615 30408] Config: (default) read_only_direct = false
[2025-05-27T10:58:57.852615 30408] Config: (default) recache = false
[2025-05-27T10:58:57.852615 30408] Config: (default) remote_only = false
[2025-05-27T10:58:57.852615 30408] Config: (default) remote_storage = 
[2025-05-27T10:58:57.852615 30408] Config: (default) reshare = false
[2025-05-27T10:58:57.852615 30408] Config: (default) run_second_cpp = true
[2025-05-27T10:58:57.852615 30408] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-05-27T10:58:57.852615 30408] Config: (default) stats = true
[2025-05-27T10:58:57.852615 30408] Config: (default) stats_log = 
[2025-05-27T10:58:57.852615 30408] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-05-27T10:58:57.852615 30408] Config: (default) umask = 
[2025-05-27T10:58:57.852675 30408] Command line: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\ccache.exe C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -o static_src\CompiledFunctionType.o -c -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace static_src\CompiledFunctionType.c
[2025-05-27T10:58:57.856285 30408] Hostname: LAPTOP-IMPU3C28
[2025-05-27T10:58:57.856310 30408] Working directory: C:/Users/<USER>/Desktop/py/dist/HZHZ~1.BUI
[2025-05-27T10:58:57.856326 30408] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe
[2025-05-27T10:58:57.856336 30408] Compiler type: gcc
[2025-05-27T10:58:57.856741 30408] Source file: static_src\CompiledFunctionType.c
[2025-05-27T10:58:57.856755 30408] Object file: static_src\CompiledFunctionType.o
[2025-05-27T10:58:57.856884 30408] Trying direct lookup
[2025-05-27T10:58:57.857093 30408] Manifest key: e628mkns198fkd38qq7niuon047e9ue8k
[2025-05-27T10:58:57.857138 30408] No e628mkns198fkd38qq7niuon047e9ue8k in local storage
[2025-05-27T10:58:57.857524 30408] Running preprocessor
[2025-05-27T10:58:57.857643 30408] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -E -o C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.fGre1x.i static_src\CompiledFunctionType.c
[2025-05-27T10:58:58.400967 130280] Got result key from preprocessor
[2025-05-27T10:58:58.400999 130280] Result key: 649etrivaj7jvr7l0mpjpug85pt7avl6i
[2025-05-27T10:58:58.401094 130280] No 649etrivaj7jvr7l0mpjpug85pt7avl6i in local storage
[2025-05-27T10:58:58.401117 130280] Running real compiler
[2025-05-27T10:58:58.402428 130280] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -c -fdiagnostics-color -o __helpers.o __helpers.c
[2025-05-27T10:58:58.430108 128568] Got result key from preprocessor
[2025-05-27T10:58:58.430137 128568] Result key: e81d3f5b43n5n7lesulaf1hq8708k0hn8
[2025-05-27T10:58:58.430226 128568] No e81d3f5b43n5n7lesulaf1hq8708k0hn8 in local storage
[2025-05-27T10:58:58.430247 128568] Running real compiler
[2025-05-27T10:58:58.430654 128568] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -c -fdiagnostics-color -o __constants.o __constants.c
[2025-05-27T10:58:58.510799 90828] Got result key from preprocessor
[2025-05-27T10:58:58.510829 90828] Result key: fe8abik4jrrrg2uo8j10pqf6kr6k49hkc
[2025-05-27T10:58:58.510890 90828] No fe8abik4jrrrg2uo8j10pqf6kr6k49hkc in local storage
[2025-05-27T10:58:58.510905 90828] Running real compiler
[2025-05-27T10:58:58.511245 90828] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -c -fdiagnostics-color -o module.multiprocessing-preLoad.o module.multiprocessing-preLoad.c
[2025-05-27T10:58:58.512361 123720] Got result key from preprocessor
[2025-05-27T10:58:58.512386 123720] Result key: 7def60a3jh5n2tdohs4v2s3e5hejnq2qq
[2025-05-27T10:58:58.512447 123720] No 7def60a3jh5n2tdohs4v2s3e5hejnq2qq in local storage
[2025-05-27T10:58:58.512462 123720] Running real compiler
[2025-05-27T10:58:58.512946 123720] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -c -fdiagnostics-color -o static_src\MainProgram.o static_src\MainProgram.c
[2025-05-27T10:58:58.514302 49360] Got result key from preprocessor
[2025-05-27T10:58:58.514333 49360] Result key: 455f2vas47sf2tdb9mct8snff23a582qo
[2025-05-27T10:58:58.514390 49360] No 455f2vas47sf2tdb9mct8snff23a582qo in local storage
[2025-05-27T10:58:58.514406 49360] Running real compiler
[2025-05-27T10:58:58.514856 49360] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -c -fdiagnostics-color -o module.multiprocessing-postLoad.o module.multiprocessing-postLoad.c
[2025-05-27T10:58:58.658980 68732] Got result key from preprocessor
[2025-05-27T10:58:58.659012 68732] Result key: 40718v6fqq5jgh2lbhn5jdkktr5fnpr1s
[2025-05-27T10:58:58.659076 68732] No 40718v6fqq5jgh2lbhn5jdkktr5fnpr1s in local storage
[2025-05-27T10:58:58.659092 68732] Running real compiler
[2025-05-27T10:58:58.659803 68732] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -c -fdiagnostics-color -o module.__main__.o module.__main__.c
[2025-05-27T10:58:58.685907 130688] Got result key from preprocessor
[2025-05-27T10:58:58.685946 130688] Result key: 1676r78natncm4i6hf9pb2ibdcf6us39u
[2025-05-27T10:58:58.686026 130688] No 1676r78natncm4i6hf9pb2ibdcf6us39u in local storage
[2025-05-27T10:58:58.686044 130688] Running real compiler
[2025-05-27T10:58:58.686434 130688] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -c -fdiagnostics-color -o module.__parents_main__.o module.__parents_main__.c
[2025-05-27T10:58:59.678849 130280] Using default compression level 1
[2025-05-27T10:58:59.679433 130280] Storing embedded entry #0 .o (2626 bytes) from __helpers.o
[2025-05-27T10:58:59.681721 130280] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_64.lock
[2025-05-27T10:58:59.682112 130280] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_64.lock
[2025-05-27T10:58:59.682416 130280] Stored 649etrivaj7jvr7l0mpjpug85pt7avl6i in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/4/9etrivaj7jvr7l0mpjpug85pt7avl6iR)
[2025-05-27T10:58:59.682487 130280] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/stats.lock
[2025-05-27T10:58:59.682617 130280] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/stats.lock
[2025-05-27T10:58:59.683091 130280] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/stats.lock
[2025-05-27T10:58:59.683186 130280] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/stats.lock
[2025-05-27T10:58:59.683202 130280] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_64.lock
[2025-05-27T10:58:59.683265 130280] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_64.lock
[2025-05-27T10:58:59.697716 130280] Added result key to manifest b58egc8omu5adrqebbun8hh4tjlcfe9k0
[2025-05-27T10:58:59.697747 130280] Using default compression level 1
[2025-05-27T10:58:59.699007 130280] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_b5.lock
[2025-05-27T10:58:59.699143 130280] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_b5.lock
[2025-05-27T10:58:59.699370 130280] Stored b58egc8omu5adrqebbun8hh4tjlcfe9k0 in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/b/5/8egc8omu5adrqebbun8hh4tjlcfe9k0M)
[2025-05-27T10:58:59.699426 130280] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/b/stats.lock
[2025-05-27T10:58:59.699523 130280] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/b/stats.lock
[2025-05-27T10:58:59.699915 130280] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/b/stats.lock
[2025-05-27T10:58:59.699990 130280] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/b/stats.lock
[2025-05-27T10:58:59.699999 130280] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_b5.lock
[2025-05-27T10:58:59.700048 130280] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_b5.lock
[2025-05-27T10:58:59.700332 130280] Result: cache_miss
[2025-05-27T10:58:59.700345 130280] Result: direct_cache_miss
[2025-05-27T10:58:59.700350 130280] Result: local_storage_miss
[2025-05-27T10:58:59.700355 130280] Result: local_storage_read_miss
[2025-05-27T10:58:59.700361 130280] Result: local_storage_read_miss
[2025-05-27T10:58:59.700366 130280] Result: local_storage_write
[2025-05-27T10:58:59.700372 130280] Result: local_storage_write
[2025-05-27T10:58:59.700377 130280] Result: preprocessed_cache_miss
[2025-05-27T10:58:59.700385 130280] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/8/stats.lock
[2025-05-27T10:58:59.700614 130280] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/8/stats.lock
[2025-05-27T10:58:59.700907 130280] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/8/stats.lock
[2025-05-27T10:58:59.700983 130280] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/8/stats.lock
[2025-05-27T10:58:59.701037 130280] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.701121 130280] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.701471 130280] No automatic cleanup needed (size 48.0 KiB, files 4, max size 5.0 GiB)
[2025-05-27T10:58:59.701487 130280] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.701569 130280] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.701709 130280] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.DzrpNq.tmp
[2025-05-27T10:58:59.701773 130280] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.s5tbPR.tmp
[2025-05-27T10:58:59.701837 130280] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.gSus7B.tmp
[2025-05-27T10:58:59.702618 130280] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.QGHRcl.i
[2025-05-27T10:58:59.721308 128568] Using default compression level 1
[2025-05-27T10:58:59.721382 128568] Storing embedded entry #0 .o (18911 bytes) from __constants.o
[2025-05-27T10:58:59.722221 128568] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e8.lock
[2025-05-27T10:58:59.722364 128568] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e8.lock
[2025-05-27T10:58:59.722570 128568] Stored e81d3f5b43n5n7lesulaf1hq8708k0hn8 in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/8/1d3f5b43n5n7lesulaf1hq8708k0hn8R)
[2025-05-27T10:58:59.722616 128568] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:58:59.722710 128568] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:58:59.723328 128568] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:58:59.723461 128568] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:58:59.723474 128568] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e8.lock
[2025-05-27T10:58:59.723518 128568] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e8.lock
[2025-05-27T10:58:59.732605 128568] Added result key to manifest a7a3i4p4tnnqhar8vmqk7euqnrqpdj02i
[2025-05-27T10:58:59.732635 128568] Using default compression level 1
[2025-05-27T10:58:59.733913 128568] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_a7.lock
[2025-05-27T10:58:59.734071 128568] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_a7.lock
[2025-05-27T10:58:59.734407 128568] Stored a7a3i4p4tnnqhar8vmqk7euqnrqpdj02i in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/7/a3i4p4tnnqhar8vmqk7euqnrqpdj02iM)
[2025-05-27T10:58:59.734465 128568] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/stats.lock
[2025-05-27T10:58:59.734586 128568] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/stats.lock
[2025-05-27T10:58:59.735176 128568] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/stats.lock
[2025-05-27T10:58:59.735266 128568] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/a/stats.lock
[2025-05-27T10:58:59.735277 128568] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_a7.lock
[2025-05-27T10:58:59.735324 128568] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_a7.lock
[2025-05-27T10:58:59.735614 128568] Result: cache_miss
[2025-05-27T10:58:59.735625 128568] Result: direct_cache_miss
[2025-05-27T10:58:59.735629 128568] Result: local_storage_miss
[2025-05-27T10:58:59.735632 128568] Result: local_storage_read_miss
[2025-05-27T10:58:59.735636 128568] Result: local_storage_read_miss
[2025-05-27T10:58:59.735639 128568] Result: local_storage_write
[2025-05-27T10:58:59.735643 128568] Result: local_storage_write
[2025-05-27T10:58:59.735646 128568] Result: preprocessed_cache_miss
[2025-05-27T10:58:59.735652 128568] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/3/8/stats.lock
[2025-05-27T10:58:59.735957 128568] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/3/8/stats.lock
[2025-05-27T10:58:59.736276 128568] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/3/8/stats.lock
[2025-05-27T10:58:59.736346 128568] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/3/8/stats.lock
[2025-05-27T10:58:59.736397 128568] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.736474 128568] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.736827 128568] No automatic cleanup needed (size 88.0 KiB, files 6, max size 5.0 GiB)
[2025-05-27T10:58:59.736842 128568] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.736935 128568] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.737076 128568] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.A0AVfi.tmp
[2025-05-27T10:58:59.737181 128568] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.UoixGl.tmp
[2025-05-27T10:58:59.737257 128568] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.EB93VN.tmp
[2025-05-27T10:58:59.737997 128568] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.JO6FWx.i
[2025-05-27T10:58:59.768356 90828] Using default compression level 1
[2025-05-27T10:58:59.768446 90828] Storing embedded entry #0 .o (56032 bytes) from module.multiprocessing-preLoad.o
[2025-05-27T10:58:59.769604 90828] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_fe.lock
[2025-05-27T10:58:59.769733 90828] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_fe.lock
[2025-05-27T10:58:59.769903 90828] Stored fe8abik4jrrrg2uo8j10pqf6kr6k49hkc in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/f/e/8abik4jrrrg2uo8j10pqf6kr6k49hkcR)
[2025-05-27T10:58:59.769934 90828] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/f/stats.lock
[2025-05-27T10:58:59.769998 90828] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/f/stats.lock
[2025-05-27T10:58:59.770582 90828] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/f/stats.lock
[2025-05-27T10:58:59.770680 90828] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/f/stats.lock
[2025-05-27T10:58:59.770698 90828] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_fe.lock
[2025-05-27T10:58:59.770762 90828] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_fe.lock
[2025-05-27T10:58:59.782234 90828] Added result key to manifest 8d419lql6d7svc0q8go1dnefbh0g8p65i
[2025-05-27T10:58:59.782253 90828] Using default compression level 1
[2025-05-27T10:58:59.783070 90828] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_8d.lock
[2025-05-27T10:58:59.783133 90828] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_8d.lock
[2025-05-27T10:58:59.783267 90828] Stored 8d419lql6d7svc0q8go1dnefbh0g8p65i in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/d/419lql6d7svc0q8go1dnefbh0g8p65iM)
[2025-05-27T10:58:59.783294 90828] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/stats.lock
[2025-05-27T10:58:59.783534 90828] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/stats.lock
[2025-05-27T10:58:59.784010 90828] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/stats.lock
[2025-05-27T10:58:59.784096 90828] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/stats.lock
[2025-05-27T10:58:59.784108 90828] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_8d.lock
[2025-05-27T10:58:59.784171 90828] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_8d.lock
[2025-05-27T10:58:59.784449 90828] Result: cache_miss
[2025-05-27T10:58:59.784465 90828] Result: direct_cache_miss
[2025-05-27T10:58:59.784473 90828] Result: local_storage_miss
[2025-05-27T10:58:59.784479 90828] Result: local_storage_read_miss
[2025-05-27T10:58:59.784486 90828] Result: local_storage_read_miss
[2025-05-27T10:58:59.784492 90828] Result: local_storage_write
[2025-05-27T10:58:59.784498 90828] Result: local_storage_write
[2025-05-27T10:58:59.784505 90828] Result: preprocessed_cache_miss
[2025-05-27T10:58:59.784514 90828] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/c/stats.lock
[2025-05-27T10:58:59.784841 90828] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/c/stats.lock
[2025-05-27T10:58:59.785271 90828] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/c/stats.lock
[2025-05-27T10:58:59.785348 90828] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/c/stats.lock
[2025-05-27T10:58:59.785419 90828] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.785523 90828] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.786111 90828] No automatic cleanup needed (size 160.0 KiB, files 8, max size 5.0 GiB)
[2025-05-27T10:58:59.786133 90828] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.786212 90828] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.786350 90828] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.83cE9t.tmp
[2025-05-27T10:58:59.786440 90828] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.qLN8RI.tmp
[2025-05-27T10:58:59.786535 90828] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.5QTzKU.tmp
[2025-05-27T10:58:59.787467 90828] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.tvBPUF.i
[2025-05-27T10:58:59.803457 123720] Using default compression level 1
[2025-05-27T10:58:59.803552 123720] Storing embedded entry #0 .o (84629 bytes) from static_src\MainProgram.o
[2025-05-27T10:58:59.804758 123720] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_7d.lock
[2025-05-27T10:58:59.804917 123720] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_7d.lock
[2025-05-27T10:58:59.805189 123720] Stored 7def60a3jh5n2tdohs4v2s3e5hejnq2qq in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/d/ef60a3jh5n2tdohs4v2s3e5hejnq2qqR)
[2025-05-27T10:58:59.805250 123720] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-05-27T10:58:59.805378 123720] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-05-27T10:58:59.805859 123720] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-05-27T10:58:59.805933 123720] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-05-27T10:58:59.805946 123720] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_7d.lock
[2025-05-27T10:58:59.805988 123720] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_7d.lock
[2025-05-27T10:58:59.808824 49360] Using default compression level 1
[2025-05-27T10:58:59.808946 49360] Storing embedded entry #0 .o (108441 bytes) from module.multiprocessing-postLoad.o
[2025-05-27T10:58:59.809993 30408] Got result key from preprocessor
[2025-05-27T10:58:59.810018 30408] Result key: 600bhrhr8oseco775iops2bmbiqlnjc72
[2025-05-27T10:58:59.810069 30408] No 600bhrhr8oseco775iops2bmbiqlnjc72 in local storage
[2025-05-27T10:58:59.810082 30408] Running real compiler
[2025-05-27T10:58:59.810227 49360] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_45.lock
[2025-05-27T10:58:59.810498 49360] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_45.lock
[2025-05-27T10:58:59.810514 30408] Executing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\13.2.0-16.0.6-11.0.1-msvcrt-r1\mingw64\bin\gcc.exe -std=c11 -flto=12 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_WIN32_WINNT=0x0501 -D__NUITKA_NO_ASSERT__ -DMS_WIN64 -D_NUITKA_CONSTANTS_FROM_RESOURCE -D_NUITKA_FROZEN=0 -D_NUITKA_EXE -D_NUITKA_FILE_REFERENCE_ORIGINAL_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -ID:\python\Lib\site-packages\nuitka\build\inline_copy\zlib -ID:\python\include -I. -ID:\python\Lib\site-packages\nuitka\build\include -ID:\python\Lib\site-packages\nuitka\build\static_src -ID:\python\Lib\site-packages\nuitka\build\inline_copy\libbacktrace -c -fdiagnostics-color -o static_src\CompiledFunctionType.o static_src\CompiledFunctionType.c
[2025-05-27T10:58:59.810818 49360] Stored 455f2vas47sf2tdb9mct8snff23a582qo in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/5/5f2vas47sf2tdb9mct8snff23a582qoR)
[2025-05-27T10:58:59.810886 49360] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:58:59.811028 49360] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:58:59.811596 49360] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:58:59.811685 49360] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:58:59.811701 49360] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_45.lock
[2025-05-27T10:58:59.811770 49360] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_45.lock
[2025-05-27T10:58:59.812053 123720] Added result key to manifest 282cks62gbm2iscelc4djloigro07hfbc
[2025-05-27T10:58:59.812067 123720] Using default compression level 1
[2025-05-27T10:58:59.813155 123720] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_28.lock
[2025-05-27T10:58:59.813278 123720] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_28.lock
[2025-05-27T10:58:59.813525 123720] Stored 282cks62gbm2iscelc4djloigro07hfbc in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/8/2cks62gbm2iscelc4djloigro07hfbcM)
[2025-05-27T10:58:59.813583 123720] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/stats.lock
[2025-05-27T10:58:59.813700 123720] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/stats.lock
[2025-05-27T10:58:59.814272 123720] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/stats.lock
[2025-05-27T10:58:59.814360 123720] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/stats.lock
[2025-05-27T10:58:59.814372 123720] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_28.lock
[2025-05-27T10:58:59.814435 123720] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_28.lock
[2025-05-27T10:58:59.814707 123720] Result: cache_miss
[2025-05-27T10:58:59.814722 123720] Result: direct_cache_miss
[2025-05-27T10:58:59.814730 123720] Result: local_storage_miss
[2025-05-27T10:58:59.814737 123720] Result: local_storage_read_miss
[2025-05-27T10:58:59.814744 123720] Result: local_storage_read_miss
[2025-05-27T10:58:59.814751 123720] Result: local_storage_write
[2025-05-27T10:58:59.814758 123720] Result: local_storage_write
[2025-05-27T10:58:59.814765 123720] Result: preprocessed_cache_miss
[2025-05-27T10:58:59.814774 123720] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/8/stats.lock
[2025-05-27T10:58:59.815113 123720] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/8/stats.lock
[2025-05-27T10:58:59.815520 123720] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/8/stats.lock
[2025-05-27T10:58:59.815583 123720] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/8/stats.lock
[2025-05-27T10:58:59.815630 123720] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.815739 123720] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.816204 123720] No automatic cleanup needed (size 364.0 KiB, files 11, max size 5.0 GiB)
[2025-05-27T10:58:59.816215 123720] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.816269 123720] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.816366 123720] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.1pR82k.tmp
[2025-05-27T10:58:59.816424 123720] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.IIFKbR.tmp
[2025-05-27T10:58:59.816480 123720] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.1Bv2uS.tmp
[2025-05-27T10:58:59.817188 123720] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.DQVlO2.i
[2025-05-27T10:58:59.818998 49360] Added result key to manifest 481afo4egnles6hfpveobv5349fb48vko
[2025-05-27T10:58:59.819027 49360] Using default compression level 1
[2025-05-27T10:58:59.819916 49360] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_48.lock
[2025-05-27T10:58:59.820030 49360] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_48.lock
[2025-05-27T10:58:59.820236 49360] Stored 481afo4egnles6hfpveobv5349fb48vko in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/8/1afo4egnles6hfpveobv5349fb48vkoM)
[2025-05-27T10:58:59.820274 49360] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:58:59.820365 49360] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:58:59.820930 49360] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:58:59.821007 49360] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:58:59.821017 49360] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_48.lock
[2025-05-27T10:58:59.821063 49360] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_48.lock
[2025-05-27T10:58:59.821145 49360] Result: cache_miss
[2025-05-27T10:58:59.821152 49360] Result: direct_cache_miss
[2025-05-27T10:58:59.821158 49360] Result: local_storage_miss
[2025-05-27T10:58:59.821163 49360] Result: local_storage_read_miss
[2025-05-27T10:58:59.821168 49360] Result: local_storage_read_miss
[2025-05-27T10:58:59.821173 49360] Result: local_storage_write
[2025-05-27T10:58:59.821178 49360] Result: local_storage_write
[2025-05-27T10:58:59.821184 49360] Result: preprocessed_cache_miss
[2025-05-27T10:58:59.821191 49360] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/0/stats.lock
[2025-05-27T10:58:59.821486 49360] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/0/stats.lock
[2025-05-27T10:58:59.821780 49360] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/0/stats.lock
[2025-05-27T10:58:59.821844 49360] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/0/stats.lock
[2025-05-27T10:58:59.821893 49360] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.821982 49360] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.822582 49360] No automatic cleanup needed (size 384.0 KiB, files 12, max size 5.0 GiB)
[2025-05-27T10:58:59.822596 49360] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.822663 49360] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:58:59.822779 49360] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.OH3td4.tmp
[2025-05-27T10:58:59.822867 49360] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.JoDc4E.tmp
[2025-05-27T10:58:59.822928 49360] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.mvuyVr.tmp
[2025-05-27T10:58:59.823561 49360] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.BYPb3O.i
[2025-05-27T10:59:01.212111 68732] Using default compression level 1
[2025-05-27T10:59:01.212488 68732] Storing embedded entry #0 .o (1381513 bytes) from module.__main__.o
[2025-05-27T10:59:01.215069 68732] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_40.lock
[2025-05-27T10:59:01.215202 68732] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_40.lock
[2025-05-27T10:59:01.215365 68732] Stored 40718v6fqq5jgh2lbhn5jdkktr5fnpr1s in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/0/718v6fqq5jgh2lbhn5jdkktr5fnpr1sR)
[2025-05-27T10:59:01.215397 68732] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:59:01.215461 68732] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:59:01.215740 68732] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:59:01.215798 68732] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/4/stats.lock
[2025-05-27T10:59:01.215806 68732] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_40.lock
[2025-05-27T10:59:01.215837 68732] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_40.lock
[2025-05-27T10:59:01.221591 68732] Added result key to manifest d74ci21q29ee2c8u0mpt27veqn8nebd38
[2025-05-27T10:59:01.221615 68732] Using default compression level 1
[2025-05-27T10:59:01.222570 68732] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_d7.lock
[2025-05-27T10:59:01.222677 68732] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_d7.lock
[2025-05-27T10:59:01.222885 68732] Stored d74ci21q29ee2c8u0mpt27veqn8nebd38 in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/7/4ci21q29ee2c8u0mpt27veqn8nebd38M)
[2025-05-27T10:59:01.222930 68732] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/stats.lock
[2025-05-27T10:59:01.223029 68732] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/stats.lock
[2025-05-27T10:59:01.223453 68732] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/stats.lock
[2025-05-27T10:59:01.223512 68732] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/d/stats.lock
[2025-05-27T10:59:01.223517 68732] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_d7.lock
[2025-05-27T10:59:01.223547 68732] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_d7.lock
[2025-05-27T10:59:01.223748 68732] Result: cache_miss
[2025-05-27T10:59:01.223756 68732] Result: direct_cache_miss
[2025-05-27T10:59:01.223759 68732] Result: local_storage_miss
[2025-05-27T10:59:01.223763 68732] Result: local_storage_read_miss
[2025-05-27T10:59:01.223766 68732] Result: local_storage_read_miss
[2025-05-27T10:59:01.223770 68732] Result: local_storage_write
[2025-05-27T10:59:01.223773 68732] Result: local_storage_write
[2025-05-27T10:59:01.223777 68732] Result: preprocessed_cache_miss
[2025-05-27T10:59:01.223782 68732] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/c/stats.lock
[2025-05-27T10:59:01.223950 68732] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/c/stats.lock
[2025-05-27T10:59:01.224136 68732] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/c/stats.lock
[2025-05-27T10:59:01.224176 68732] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/c/stats.lock
[2025-05-27T10:59:01.224204 68732] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:01.224249 68732] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:01.224645 68732] No automatic cleanup needed (size 1.7 MiB, files 14, max size 5.0 GiB)
[2025-05-27T10:59:01.224661 68732] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:01.224734 68732] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:01.224843 68732] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.InH56x.tmp
[2025-05-27T10:59:01.224910 68732] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.iJPhZ8.tmp
[2025-05-27T10:59:01.225069 68732] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.HhCP59.tmp
[2025-05-27T10:59:01.226121 68732] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.090JHk.i
[2025-05-27T10:59:01.241465 130688] Using default compression level 1
[2025-05-27T10:59:01.241951 130688] Storing embedded entry #0 .o (1385323 bytes) from module.__parents_main__.o
[2025-05-27T10:59:01.245586 130688] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_16.lock
[2025-05-27T10:59:01.245732 130688] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_16.lock
[2025-05-27T10:59:01.245992 130688] Stored 1676r78natncm4i6hf9pb2ibdcf6us39u in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/1/6/76r78natncm4i6hf9pb2ibdcf6us39uR)
[2025-05-27T10:59:01.246041 130688] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/1/stats.lock
[2025-05-27T10:59:01.246133 130688] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/1/stats.lock
[2025-05-27T10:59:01.246450 130688] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/1/stats.lock
[2025-05-27T10:59:01.246526 130688] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/1/stats.lock
[2025-05-27T10:59:01.246538 130688] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_16.lock
[2025-05-27T10:59:01.246589 130688] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_16.lock
[2025-05-27T10:59:01.255370 130688] Added result key to manifest 7126qr453k41pahutajddiles2i4i1934
[2025-05-27T10:59:01.255398 130688] Using default compression level 1
[2025-05-27T10:59:01.256517 130688] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_71.lock
[2025-05-27T10:59:01.256619 130688] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_71.lock
[2025-05-27T10:59:01.256766 130688] Stored 7126qr453k41pahutajddiles2i4i1934 in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/1/26qr453k41pahutajddiles2i4i1934M)
[2025-05-27T10:59:01.256793 130688] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-05-27T10:59:01.256882 130688] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-05-27T10:59:01.257234 130688] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-05-27T10:59:01.257291 130688] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/stats.lock
[2025-05-27T10:59:01.257295 130688] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_71.lock
[2025-05-27T10:59:01.257325 130688] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_71.lock
[2025-05-27T10:59:01.257385 130688] Result: cache_miss
[2025-05-27T10:59:01.257390 130688] Result: direct_cache_miss
[2025-05-27T10:59:01.257394 130688] Result: local_storage_miss
[2025-05-27T10:59:01.257397 130688] Result: local_storage_read_miss
[2025-05-27T10:59:01.257400 130688] Result: local_storage_read_miss
[2025-05-27T10:59:01.257404 130688] Result: local_storage_write
[2025-05-27T10:59:01.257407 130688] Result: local_storage_write
[2025-05-27T10:59:01.257411 130688] Result: preprocessed_cache_miss
[2025-05-27T10:59:01.257415 130688] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/0/stats.lock
[2025-05-27T10:59:01.257573 130688] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/0/stats.lock
[2025-05-27T10:59:01.257794 130688] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/0/stats.lock
[2025-05-27T10:59:01.257834 130688] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/8/0/stats.lock
[2025-05-27T10:59:01.257864 130688] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:01.257910 130688] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:01.258299 130688] No automatic cleanup needed (size 3.0 MiB, files 16, max size 5.0 GiB)
[2025-05-27T10:59:01.258308 130688] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:01.258357 130688] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:01.258462 130688] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.FwpB6a.tmp
[2025-05-27T10:59:01.258521 130688] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.sKV0gR.tmp
[2025-05-27T10:59:01.258581 130688] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.UOyUa7.tmp
[2025-05-27T10:59:01.259371 130688] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.lDfMSN.i
[2025-05-27T10:59:03.790570 30408] Using default compression level 1
[2025-05-27T10:59:03.791318 30408] Storing embedded entry #0 .o (3688762 bytes) from static_src\CompiledFunctionType.o
[2025-05-27T10:59:03.798310 30408] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_60.lock
[2025-05-27T10:59:03.798639 30408] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_60.lock
[2025-05-27T10:59:03.798830 30408] Stored 600bhrhr8oseco775iops2bmbiqlnjc72 in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/0/0bhrhr8oseco775iops2bmbiqlnjc72R)
[2025-05-27T10:59:03.798870 30408] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/stats.lock
[2025-05-27T10:59:03.798976 30408] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/stats.lock
[2025-05-27T10:59:03.799561 30408] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/stats.lock
[2025-05-27T10:59:03.799645 30408] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/6/stats.lock
[2025-05-27T10:59:03.799661 30408] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_60.lock
[2025-05-27T10:59:03.799714 30408] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_60.lock
[2025-05-27T10:59:03.807812 30408] Added result key to manifest e628mkns198fkd38qq7niuon047e9ue8k
[2025-05-27T10:59:03.807836 30408] Using default compression level 1
[2025-05-27T10:59:03.808825 30408] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e6.lock
[2025-05-27T10:59:03.808938 30408] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e6.lock
[2025-05-27T10:59:03.809134 30408] Stored e628mkns198fkd38qq7niuon047e9ue8k in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/6/28mkns198fkd38qq7niuon047e9ue8kM)
[2025-05-27T10:59:03.809179 30408] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:59:03.809276 30408] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:59:03.809777 30408] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:59:03.809833 30408] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/e/stats.lock
[2025-05-27T10:59:03.809839 30408] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e6.lock
[2025-05-27T10:59:03.809866 30408] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_e6.lock
[2025-05-27T10:59:03.809937 30408] Result: cache_miss
[2025-05-27T10:59:03.809942 30408] Result: direct_cache_miss
[2025-05-27T10:59:03.809945 30408] Result: local_storage_miss
[2025-05-27T10:59:03.809949 30408] Result: local_storage_read_miss
[2025-05-27T10:59:03.809953 30408] Result: local_storage_read_miss
[2025-05-27T10:59:03.809956 30408] Result: local_storage_write
[2025-05-27T10:59:03.809960 30408] Result: local_storage_write
[2025-05-27T10:59:03.809963 30408] Result: preprocessed_cache_miss
[2025-05-27T10:59:03.809968 30408] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/8/stats.lock
[2025-05-27T10:59:03.810157 30408] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/8/stats.lock
[2025-05-27T10:59:03.810372 30408] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/8/stats.lock
[2025-05-27T10:59:03.810413 30408] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/c/8/stats.lock
[2025-05-27T10:59:03.810446 30408] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:03.810498 30408] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:03.810908 30408] No automatic cleanup needed (size 6.1 MiB, files 18, max size 5.0 GiB)
[2025-05-27T10:59:03.810919 30408] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:03.810964 30408] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-05-27T10:59:03.811039 30408] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.pzLVVp.tmp
[2025-05-27T10:59:03.811084 30408] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stdout.tmp.EEG6hb.tmp
[2025-05-27T10:59:03.811134 30408] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.p9cSXz.tmp
[2025-05-27T10:59:03.811989 30408] Unlink C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.fGre1x.i
