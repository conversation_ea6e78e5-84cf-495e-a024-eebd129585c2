import os
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from multiprocessing import Pool
from decimal import Decimal, getcontext
import time
import shutil
import logging
import traceback
from pathlib import Path
import json
from datetime import datetime
import psutil
import hashlib
import threading
import signal
import sys
import gc
from contextlib import contextmanager
from typing import Dict, Optional, Any, Tuple
import configparser


# 配置日志系统
def setup_logging():
    """设置日志配置"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    log_filename = log_dir / f"hzhz_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# 全局日志对象
logger = setup_logging()

class ConfigManager:
    """配置管理类"""

    def __init__(self, config_file="disaster_config.ini"):
        self.config_file = Path(config_file)
        self.config = configparser.ConfigParser()
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            try:
                self.config.read(self.config_file, encoding='utf-8')
                logger.info(f"成功加载配置文件: {self.config_file}")
            except Exception as e:
                logger.warning(f"加载配置文件失败: {e}")
                self.create_default_config()
        else:
            self.create_default_config()

    def create_default_config(self):
        """创建默认配置"""
        self.config['DISASTER_RECOVERY'] = {
            'max_retries': '3',
            'retry_delay': '2',
            'retry_backoff': '2',
            'checkpoint_interval': '10',
            'auto_backup': 'true',
            'backup_count': '3'
        }

        self.config['RESOURCE_MONITORING'] = {
            'memory_threshold': '80',
            'disk_threshold': '90',
            'check_interval': '30'
        }

        self.config['FILE_OPERATIONS'] = {
            'temp_suffix': '.tmp',
            'backup_suffix': '.bak',
            'verify_integrity': 'true'
        }

        self.save_config()

    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            logger.info(f"配置文件已保存: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")

    def get(self, section: str, key: str, fallback=None):
        """获取配置值"""
        return self.config.get(section, key, fallback=fallback)

    def getint(self, section: str, key: str, fallback=0):
        """获取整数配置值"""
        return self.config.getint(section, key, fallback=fallback)

    def getboolean(self, section: str, key: str, fallback=False):
        """获取布尔配置值"""
        return self.config.getboolean(section, key, fallback=fallback)

class ResourceMonitor:
    """资源监控类"""

    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.memory_threshold = self.config.getint('RESOURCE_MONITORING', 'memory_threshold', 80)
        self.disk_threshold = self.config.getint('RESOURCE_MONITORING', 'disk_threshold', 90)
        self._monitoring = False
        self._monitor_thread = None

    def check_memory_usage(self) -> Tuple[float, bool]:
        """检查内存使用率"""
        try:
            memory = psutil.virtual_memory()
            usage_percent = memory.percent
            is_critical = usage_percent > self.memory_threshold

            if is_critical:
                logger.warning(f"内存使用率过高: {usage_percent:.1f}% (阈值: {self.memory_threshold}%)")

            return usage_percent, is_critical
        except Exception as e:
            logger.error(f"检查内存使用率失败: {e}")
            return 0.0, False

    def check_disk_space(self, path: str) -> Tuple[float, bool]:
        """检查磁盘空间"""
        try:
            disk = psutil.disk_usage(path)
            usage_percent = (disk.used / disk.total) * 100
            is_critical = usage_percent > self.disk_threshold

            if is_critical:
                logger.warning(f"磁盘空间不足: {usage_percent:.1f}% (阈值: {self.disk_threshold}%)")

            return usage_percent, is_critical
        except Exception as e:
            logger.error(f"检查磁盘空间失败: {e}")
            return 0.0, False

    def force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            collected = gc.collect()
            logger.info(f"垃圾回收完成，回收对象数: {collected}")
        except Exception as e:
            logger.error(f"垃圾回收失败: {e}")

    def get_available_memory_mb(self) -> float:
        """获取可用内存(MB)"""
        try:
            memory = psutil.virtual_memory()
            return memory.available / (1024 * 1024)
        except Exception as e:
            logger.error(f"获取可用内存失败: {e}")
            return 0.0

    def start_monitoring(self):
        """开始资源监控"""
        if self._monitoring:
            return

        self._monitoring = True
        check_interval = self.config.getint('RESOURCE_MONITORING', 'check_interval', 30)

        def monitor_loop():
            while self._monitoring:
                try:
                    # 检查内存
                    _, memory_critical = self.check_memory_usage()
                    if memory_critical:
                        self.force_garbage_collection()

                    # 检查磁盘空间
                    _, disk_critical = self.check_disk_space('.')
                    if disk_critical:
                        logger.critical("磁盘空间严重不足，建议立即清理")

                    time.sleep(check_interval)
                except Exception as e:
                    logger.error(f"资源监控异常: {e}")
                    time.sleep(check_interval)

        self._monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("资源监控已启动")

    def stop_monitoring(self):
        """停止资源监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("资源监控已停止")

class DisasterRecovery:
    """增强的容灾恢复类"""

    def __init__(self, checkpoint_file="checkpoint.json", config_manager: Optional[ConfigManager] = None):
        self.checkpoint_file = Path(checkpoint_file)
        self.config = config_manager or ConfigManager()
        self.checkpoint_data = self.load_checkpoint()
        self.backup_count = self.config.getint('DISASTER_RECOVERY', 'backup_count', 3)
        self.auto_backup = self.config.getboolean('DISASTER_RECOVERY', 'auto_backup', True)
        self._lock = threading.Lock()

    def load_checkpoint(self):
        """加载检查点数据"""
        if self.checkpoint_file.exists():
            try:
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"成功加载检查点文件: {self.checkpoint_file}")
                return data
            except Exception as e:
                logger.warning(f"加载检查点失败: {e}")
                # 尝试从备份恢复
                return self._restore_from_backup()
        return {}

    def _restore_from_backup(self):
        """从备份文件恢复检查点"""
        for i in range(1, self.backup_count + 1):
            backup_file = Path(f"{self.checkpoint_file}.bak{i}")
            if backup_file.exists():
                try:
                    with open(backup_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    logger.info(f"从备份文件恢复检查点: {backup_file}")
                    return data
                except Exception as e:
                    logger.warning(f"备份文件 {backup_file} 损坏: {e}")
        return {}

    def _create_backup(self):
        """创建检查点备份"""
        if not self.auto_backup or not self.checkpoint_file.exists():
            return

        try:
            # 轮转备份文件
            for i in range(self.backup_count, 1, -1):
                old_backup = Path(f"{self.checkpoint_file}.bak{i-1}")
                new_backup = Path(f"{self.checkpoint_file}.bak{i}")
                if old_backup.exists():
                    shutil.move(str(old_backup), str(new_backup))

            # 创建新的备份
            backup_file = Path(f"{self.checkpoint_file}.bak1")
            shutil.copy2(str(self.checkpoint_file), str(backup_file))
            logger.debug(f"创建检查点备份: {backup_file}")
        except Exception as e:
            logger.warning(f"创建备份失败: {e}")

    def save_checkpoint(self, data):
        """保存检查点数据"""
        with self._lock:
            try:
                # 创建备份
                self._create_backup()

                # 原子写入
                temp_file = Path(f"{self.checkpoint_file}.tmp")
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                # 原子移动
                shutil.move(str(temp_file), str(self.checkpoint_file))
                logger.debug("检查点数据已保存")
            except Exception as e:
                logger.error(f"保存检查点失败: {e}")
                # 清理临时文件
                temp_file = Path(f"{self.checkpoint_file}.tmp")
                if temp_file.exists():
                    try:
                        temp_file.unlink()
                    except:
                        pass

    def is_completed(self, task_id: str) -> bool:
        """检查任务是否已完成"""
        return self.checkpoint_data.get(task_id, {}).get('completed', False)

    def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """获取任务进度信息"""
        return self.checkpoint_data.get(task_id, {})

    def mark_completed(self, task_id: str, result_path: str, metadata: Optional[Dict] = None):
        """标记任务完成"""
        task_data = {
            'completed': True,
            'result_path': str(result_path),
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata or {}
        }

        # 验证结果文件
        if Path(result_path).exists():
            task_data['file_size'] = Path(result_path).stat().st_size
            task_data['file_hash'] = self._calculate_file_hash(result_path)

        self.checkpoint_data[task_id] = task_data
        self.save_checkpoint(self.checkpoint_data)

    def mark_progress(self, task_id: str, progress_data: Dict[str, Any]):
        """标记任务进度"""
        if task_id not in self.checkpoint_data:
            self.checkpoint_data[task_id] = {'completed': False}

        self.checkpoint_data[task_id].update({
            'progress': progress_data,
            'last_update': datetime.now().isoformat()
        })
        self.save_checkpoint(self.checkpoint_data)

    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.warning(f"计算文件哈希失败: {e}")
            return ""

    def verify_file_integrity(self, task_id: str) -> bool:
        """验证文件完整性"""
        task_data = self.checkpoint_data.get(task_id, {})
        if not task_data.get('completed'):
            return False

        result_path = task_data.get('result_path')
        expected_hash = task_data.get('file_hash')

        if not result_path or not expected_hash:
            return False

        if not Path(result_path).exists():
            logger.warning(f"结果文件不存在: {result_path}")
            return False

        current_hash = self._calculate_file_hash(result_path)
        if current_hash != expected_hash:
            logger.warning(f"文件完整性验证失败: {result_path}")
            return False

        return True

    def cleanup_old_checkpoints(self, days: int = 7):
        """清理旧的检查点数据"""
        cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)

        to_remove = []
        for task_id, task_data in self.checkpoint_data.items():
            timestamp_str = task_data.get('timestamp')
            if timestamp_str:
                try:
                    task_time = datetime.fromisoformat(timestamp_str).timestamp()
                    if task_time < cutoff_time:
                        to_remove.append(task_id)
                except:
                    pass

        for task_id in to_remove:
            del self.checkpoint_data[task_id]

        if to_remove:
            self.save_checkpoint(self.checkpoint_data)
            logger.info(f"清理了 {len(to_remove)} 个旧检查点")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_tasks = len(self.checkpoint_data)
        completed_tasks = sum(1 for task in self.checkpoint_data.values() if task.get('completed'))

        return {
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'pending_tasks': total_tasks - completed_tasks,
            'completion_rate': completed_tasks / total_tasks if total_tasks > 0 else 0
        }

def retry_on_failure(max_retries=3, delay=1, backoff=2, exceptions=(Exception,)):
    """增强的重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            retries = 0
            current_delay = delay

            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    retries += 1

                    if retries >= max_retries:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        raise

                    logger.warning(f"函数 {func.__name__} 第 {retries} 次失败，{current_delay}秒后重试: {e}")

                    # 在重试前进行垃圾回收
                    if retries > 1:
                        gc.collect()

                    time.sleep(current_delay)
                    current_delay *= backoff
                except Exception as e:
                    # 对于不在重试范围内的异常，直接抛出
                    logger.error(f"函数 {func.__name__} 发生不可重试的异常: {e}")
                    raise

            return None
        return wrapper
    return decorator

@contextmanager
def safe_file_context(file_path: str, config_manager: Optional[ConfigManager] = None):
    """安全文件操作上下文管理器"""
    config = config_manager or ConfigManager()
    temp_suffix = config.get('FILE_OPERATIONS', 'temp_suffix', '.tmp')
    backup_suffix = config.get('FILE_OPERATIONS', 'backup_suffix', '.bak')

    original_path = Path(file_path)
    temp_path = Path(f"{file_path}{temp_suffix}")
    backup_path = Path(f"{file_path}{backup_suffix}")

    try:
        # 如果原文件存在，创建备份
        if original_path.exists():
            shutil.copy2(str(original_path), str(backup_path))
            logger.debug(f"创建文件备份: {backup_path}")

        yield str(temp_path)

        # 操作成功，原子移动临时文件到目标位置
        if temp_path.exists():
            shutil.move(str(temp_path), str(original_path))
            logger.info(f"文件安全保存到: {original_path}")

            # 删除备份文件
            if backup_path.exists():
                backup_path.unlink()

    except Exception as e:
        logger.error(f"文件操作失败: {e}")

        # 清理临时文件
        if temp_path.exists():
            try:
                temp_path.unlink()
            except:
                pass

        # 如果有备份，尝试恢复
        if backup_path.exists() and not original_path.exists():
            try:
                shutil.move(str(backup_path), str(original_path))
                logger.info(f"从备份恢复文件: {original_path}")
            except Exception as restore_error:
                logger.error(f"从备份恢复失败: {restore_error}")

        raise
    finally:
        # 清理备份文件（如果操作失败且原文件存在）
        if backup_path.exists() and original_path.exists():
            try:
                backup_path.unlink()
            except:
                pass

def safe_file_operation(operation, *args, **kwargs):
    """增强的安全文件操作"""
    config = ConfigManager()
    verify_integrity = config.getboolean('FILE_OPERATIONS', 'verify_integrity', True)

    # 确定文件路径
    file_path = None
    if operation.__name__ in ['save', 'to_csv', 'to_excel']:
        file_path = args[0] if args else kwargs.get('filename', kwargs.get('path'))

    if not file_path:
        # 如果不是文件操作，直接执行
        return operation(*args, **kwargs)

    # 检查磁盘空间
    monitor = ResourceMonitor(config)
    disk_usage, is_critical = monitor.check_disk_space(os.path.dirname(file_path) or '.')
    if is_critical:
        raise RuntimeError(f"磁盘空间不足，无法执行文件操作: {disk_usage:.1f}%")

    with safe_file_context(file_path, config) as temp_path:
        # 修改参数以使用临时文件
        if args:
            new_args = (temp_path,) + args[1:]
        else:
            new_kwargs = kwargs.copy()
            if 'filename' in new_kwargs:
                new_kwargs['filename'] = temp_path
            elif 'path' in new_kwargs:
                new_kwargs['path'] = temp_path
            new_args = args
            kwargs = new_kwargs

        result = operation(*new_args, **kwargs)

        # 验证文件完整性
        if verify_integrity and Path(temp_path).exists():
            file_size = Path(temp_path).stat().st_size
            if file_size == 0:
                raise RuntimeError(f"生成的文件为空: {temp_path}")
            logger.debug(f"文件大小验证通过: {file_size} bytes")

        return result

class ProcessPoolManager:
    """进程池管理器，提供容错和监控功能"""

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        self.config = config_manager or ConfigManager()
        self.resource_monitor = ResourceMonitor(self.config)
        self._pool = None
        self._active_tasks = 0
        self._lock = threading.Lock()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()

    def create_pool(self, processes: int):
        """创建进程池"""
        if self._pool is not None:
            self.cleanup()

        # 检查系统资源
        available_memory = self.resource_monitor.get_available_memory_mb()
        if available_memory < 500:  # 少于500MB可用内存
            logger.warning(f"可用内存较少: {available_memory:.1f}MB，建议减少进程数")

        try:
            self._pool = Pool(processes=processes)
            logger.info(f"创建进程池，进程数: {processes}")
            return self._pool
        except Exception as e:
            logger.error(f"创建进程池失败: {e}")
            raise

    def submit_task(self, func, *args, **kwargs):
        """提交任务到进程池"""
        if self._pool is None:
            raise RuntimeError("进程池未初始化")

        with self._lock:
            self._active_tasks += 1

        try:
            result = self._pool.apply_async(func, args, kwargs)
            return result
        except Exception as e:
            with self._lock:
                self._active_tasks -= 1
            logger.error(f"提交任务失败: {e}")
            raise

    def wait_for_completion(self, timeout: Optional[float] = None):
        """等待所有任务完成"""
        if self._pool is None:
            return

        try:
            self._pool.close()
            self._pool.join()
            logger.info("所有任务已完成")
        except Exception as e:
            logger.error(f"等待任务完成时发生错误: {e}")
            self.force_cleanup()

    def force_cleanup(self):
        """强制清理进程池"""
        if self._pool is not None:
            try:
                self._pool.terminate()
                self._pool.join()
                logger.warning("强制终止进程池")
            except Exception as e:
                logger.error(f"强制清理进程池失败: {e}")

    def cleanup(self):
        """清理资源"""
        if self._pool is not None:
            try:
                self._pool.close()
                self._pool.join()
            except:
                self.force_cleanup()
            finally:
                self._pool = None
                self._active_tasks = 0

@retry_on_failure(max_retries=3, delay=2)
def dict_sj():
    try:
        logger.info("开始加载数据字典文件: 工作簿12.xlsx")
        workbook = openpyxl.load_workbook(filename='工作簿12.xlsx')
        # 获取默认工作表
        sheet = workbook['Sheet1']
        # 将数据转换为字典
        data = {}
        row_count = 0
        for row in sheet.iter_rows(min_row=2, values_only=True):
            if row[0] is not None:  # 确保键不为空
                key = str(row[0]).lower()  # 转换为字符串并小写
                value = row[1:]
                data[key] = value
                row_count += 1

        logger.info(f"成功加载数据字典，共 {row_count} 条记录")

        # 测试数据访问
        test_key = 'hz_ZJ_775'.lower()
        if test_key in data:
            logger.info(f"测试数据访问成功: {test_key} -> {data[test_key]}")
        else:
            logger.warning(f"测试键 {test_key} 不存在于数据字典中")

        return data

    except FileNotFoundError:
        logger.error("数据字典文件 '工作簿12.xlsx' 未找到")
        raise
    except Exception as e:
        logger.error(f"加载数据字典时发生错误: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise
def format_excel(ws):
    # 设置字体和颜色,文本对齐方式
    header_font = Font(name='Microsoft YaHei UI', size=10, bold=True, color='FFFFFF')
    content_font = Font(name='Microsoft YaHei UI', size=10)
    header_fill = PatternFill(start_color='1f4e78', end_color='1f4e78', fill_type='solid')
    content_fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
    highlight_fill = PatternFill(start_color='f8cbad', end_color='f8cbad', fill_type='solid')

    # 格式化表头
    for row in ws.iter_rows(min_row=1, max_row=1):
        for cell in row:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    # 添加自动筛选
    ws.auto_filter.ref = ws.dimensions
    # 格式化内容
    for row in ws.iter_rows(min_row=2):
        for cell in row:
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=False)
            cell.font = content_font
            cell.fill = content_fill
    # 设置行高和列宽
    ws.row_dimensions[1].height = 35  # 表头行
    for r in range(2, ws.max_row):    # 内容行
        ws.row_dimensions[r].height = 25
    # 优化列宽
    dict_width = {'A': 8, 'B': 8, 'C': 20, 'D': 20, 'E': 50, 'F': 50, 'G': 12,'H':12}
    # 格式化各列
    for col in ws.columns:
        column = col[0].column_letter
        for i, cell in enumerate(col):
            if i > 0 and (column == 'A'):  # 序号列
                cell.font = Font(name='Microsoft YaHei UI', size=10, color='0000ff', underline='single')
            elif i > 0 and (column == 'F' or column == 'E' ):  # 问题和规则依据列
                cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=False)
            elif i > 0 and (column in ['H', 'G']):  # 金额列
                #cell.number_format = numbers.FORMAT_NUMBER_COMMA_SEPARATED1
                cell.fill = PatternFill(start_color='fce4d6', end_color='fce4d6', fill_type='solid')
        ws.column_dimensions[column].width = dict_width.get(column, 12)  # 默认宽度12
    # 设置边框
    thin_border = Border(left=Side(style='thin', color='7F7F7F'),
                        right=Side(style='thin', color='7F7F7F'),
                        top=Side(style='thin', color='7F7F7F'),
                        bottom=Side(style='thin', color='7F7F7F'))
    # 应用边框
    for row in ws.rows:
        for cell in row:
            cell.border = thin_border

    # 处理最后一行
    ws.merge_cells(start_row=ws.max_row, start_column=2, end_row=ws.max_row, end_column=5)
    for cell in ws[ws.max_row]:
        cell.font = Font(bold=True, name='Microsoft YaHei UI', size=10)
        cell.fill = highlight_fill
    ws.cell(row=ws.max_row, column=4).alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[ws.max_row].height = 40

    # 冻结首行
    ws.freeze_panes = 'A2'


    return ws

@retry_on_failure(max_retries=3, delay=2, exceptions=(FileNotFoundError, PermissionError, OSError))
def merge_xlsx_files(source_folder, target_file, data, config_manager: Optional[ConfigManager] = None):
    """增强的Excel文件合并函数，带完整容灾机制"""
    config = config_manager or ConfigManager()
    recovery = DisasterRecovery(config_manager=config)
    monitor = ResourceMonitor(config)

    # 启动资源监控
    monitor.start_monitoring()

    try:
        logger.info(f"开始处理文件夹: {source_folder}")

        # 预检查
        if not os.path.exists(source_folder):
            logger.warning(f"源文件夹不存在: {source_folder}")
            return

        # 检查磁盘空间
        disk_usage, is_critical = monitor.check_disk_space(os.path.dirname(target_file) or '.')
        if is_critical:
            raise RuntimeError(f"目标目录磁盘空间不足: {disk_usage:.1f}%")

        # 检查文件夹中的文件
        files_in_folder = os.listdir(source_folder)
        excel_files = [f for f in files_in_folder if f.lower().endswith('.xlsx')]

        if not excel_files:
            logger.warning(f"{source_folder}中没有Excel文件！跳过")
            return

        # 检查任务状态
        task_id = f"{os.path.basename(source_folder)}_merge"
        if recovery.is_completed(task_id):
            # 验证文件完整性
            if recovery.verify_file_integrity(task_id):
                logger.info(f"任务 {task_id} 已完成且文件完整，跳过")
                return
            else:
                logger.warning(f"任务 {task_id} 文件完整性验证失败，重新处理")

        # 记录任务开始
        recovery.mark_progress(task_id, {
            'status': 'started',
            'total_files': len(excel_files),
            'processed_files': 0
        })

        logger.info(f"找到 {len(excel_files)} 个Excel文件")

        # 创建一个新工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = '汇总'
        ws.append(['序号','分类', '违规类型', '问题项目', '问题情形描述','认定依据','人次','金额'])

        # 遍历源文件夹中的所有文件
        sheet_num = 1
        processed_files = 0
        failed_files = []

        # 定期检查点间隔
        checkpoint_interval = config.getint('DISASTER_RECOVERY', 'checkpoint_interval', 10)

        for i, filename in enumerate(excel_files):
            file_path = os.path.join(source_folder, filename)
            logger.info(f"正在处理文件 ({i+1}/{len(excel_files)}): {filename}")

            # 检查内存使用情况
            _, memory_critical = monitor.check_memory_usage()
            if memory_critical:
                logger.warning("内存使用率过高，执行垃圾回收")
                monitor.force_garbage_collection()

            try:
                workbook = openpyxl.load_workbook(file_path, read_only=True)
                processed_files += 1

                # 更新进度
                if processed_files % checkpoint_interval == 0:
                    recovery.mark_progress(task_id, {
                        'status': 'processing',
                        'total_files': len(excel_files),
                        'processed_files': processed_files,
                        'current_file': filename
                    })

            except Exception as e:
                logger.warning(f"无法加载文件 {file_path}: {e}")
                failed_files.append(filename)
                continue
            # 遍历工作簿中的所有工作表
            for sheetname in workbook.sheetnames:
                # 复制工作表到新工作簿中的单独工作表中
                sheet = workbook[sheetname]
                new_ws = wb.create_sheet(f"{sheet_num}")
                new_ws.title = f"{sheet_num}"
                # 遍历单元格，将值复制到新的单元格中
                for row in sheet.iter_rows():
                    new_ws.append([cell.value for cell in row])
                last_col = new_ws.max_column
                second_last_col = last_col - 1
                total_violations = 0
                total_amount = 0
                # for col in new_ws.iter_cols(min_col=second_last_col, max_col=last_col):
                #     for cell in col:
                #         if cell.row == 1:
                #             continue
                #         if cell.column == second_last_col:
                #             if cell.value is not None:
                #              try:
                #                 total_violations += float(cell.value)
                #                 cell.value = float(cell.value)
                #                 #设置为数字
                #              except ValueError:
                #                  total_violations = 0
                #                  total_amount = 0
                #                  print(cell.value)
                #                  print('最后不是数字:'+ os.path.join(source_folder, filename))
                #                  break
                #         elif cell.column == last_col:
                #             if cell.value is not None:
                #              try:
                #                  # 设置为数字
                #                  total_amount += float(cell.value)
                #                  cell.value = float(cell.value)
                #              except ValueError:
                #                  total_violations = 0
                #                  total_amount = 0
                #                  print(cell.value)
                #                  print('最后不是数字:'+ os.path.join(source_folder, filename))
                #                  break
                # 查找money_rules和bill_id列
                money_col = None
                bill_id_col = None
                for col in new_ws.iter_cols(min_row=1, max_row=1):  # 只在表头行查找
                    for cell in col:
                        if cell.value == "违规金额":
                            money_col = cell.column
                        elif cell.value == "单据号":
                            bill_id_col = cell.column
                        #找不到违规金额取最后一列
                    if money_col is None:
                        money_col = last_col

                getcontext().prec = 10  # 设置足够高的精度

                total_amount = Decimal('0')
                distinct_count = 0
                bill_ids = set()
                if money_col is not None and bill_id_col is not None:
                    for row in new_ws.iter_rows(min_row=2):
                        # 处理金额列
                        money_cell = row[money_col-1]
                        if money_cell.value is not None and str(money_cell.value).strip():
                            try:
                                amount = Decimal(str(money_cell.value).strip())
                                total_amount += amount
                            except (ValueError, TypeError) as e:
                                print(f'金额列包含无效数据，文件: {os.path.join(source_folder, filename)}，行: {row[0].row}，值: {money_cell.value}，错误: {str(e)}')

                        # 处理ID列
                        bill_cell = row[bill_id_col-1]
                        if bill_cell.value is not None and str(bill_cell.value).strip():
                            bill_ids.add(str(bill_cell.value).strip())

                    distinct_count = len(bill_ids)
                elif money_col is not None:  # 只有金额列的情况
                    for row in new_ws.iter_rows(min_row=2):
                        money_cell = row[money_col-1]
                        if money_cell.value is not None and str(money_cell.value).strip():
                            try:
                                amount = Decimal(str(money_cell.value).strip())
                                total_amount += amount
                            except (ValueError, TypeError) as e:
                                print(f'金额列包含无效数据，文件: {os.path.join(source_folder, filename)}，行: {row[0].row}，值: {money_cell.value}，错误: {str(e)}')
                elif bill_id_col is not None:  # 只有ID列的情况
                    bill_ids = set()
                    for row in new_ws.iter_rows(min_row=2):
                        bill_cell = row[bill_id_col-1]
                        if bill_cell.value is not None:
                            bill_ids.add(str(bill_cell.value))
                    distinct_count = len(bill_ids)

                #(['序号', '违规类型', '问题项目', '问题情形描述','认定依据','人次','金额'])
                gzmc = filename[:-5].lower()
                if data.get(gzmc, ['','','','','',''])[6].lower() == 'd':
                    total_amount = '\\N'
                    distinct_count = '\\N'
                ws.append([f'=HYPERLINK("#{sheet_num}!A1", "{sheet_num}")',
                            data.get(gzmc, ['','','','','',''])[6],
                          data.get(gzmc, ['',gzmc,'检查字典表','','',''])[1],
                          data.get(gzmc, ['','','','','',''])[2],
                          data.get(gzmc, ['','','','','',''])[4],
                          data.get(gzmc, ['','','','','',''])[5],
                          distinct_count,
                          total_amount])
                sheet_num += 1

        # 保存新工作簿
        ws.append(['合计', f'共计-{ws.max_row - 1}-条违规项目', '', f'', '',
                  f'=SUM(G2:G{ws.max_row})',  # 人次总和
                  f'=SUM(H2:H{ws.max_row})'])  # 金额总和
        ws = format_excel(ws)
        wb.properties.creator="飞"

        # 使用安全文件操作保存
        safe_file_operation(wb.save, target_file, config)

        # 记录处理结果
        logger.info(f"成功处理 {processed_files} 个文件，失败 {len(failed_files)} 个文件")
        if failed_files:
            logger.warning(f"失败的文件: {failed_files}")

        # 标记任务完成，包含元数据
        metadata = {
            'total_files': len(excel_files),
            'processed_files': processed_files,
            'failed_files': failed_files,
            'sheets_created': sheet_num - 1
        }
        recovery.mark_completed(task_id, target_file, metadata)
        logger.info(f"文件合并完成: {target_file}")

    except Exception as e:
        logger.error(f"合并文件时发生错误: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")

        # 记录失败状态
        recovery.mark_progress(task_id, {
            'status': 'failed',
            'error': str(e),
            'processed_files': processed_files,
            'failed_files': failed_files
        })
        raise
    finally:
        # 停止资源监控
        monitor.stop_monitoring()

        # 清理工作簿对象
        try:
            if 'wb' in locals():
                wb.close()
        except:
            pass
def traverse_folders(folder_path, save_path, data):
    """增强的文件夹遍历函数，带完整容灾机制"""
    config = ConfigManager()
    recovery = DisasterRecovery(config_manager=config)
    monitor = ResourceMonitor(config)

    # 启动资源监控
    monitor.start_monitoring()

    try:
        # 获取CPU核心数作为默认进程数
        cpu_count = os.cpu_count() or 4
        suggested_processes = min(cpu_count, 8)  # 限制最大进程数

        print(f"检测到 {cpu_count} 个CPU核心，建议使用 {suggested_processes} 个进程")
        process_count = int(input(f"请输入进程数 (建议: {suggested_processes}): ") or suggested_processes)

        # 检查系统资源
        available_memory = monitor.get_available_memory_mb()
        if available_memory < process_count * 200:  # 每个进程至少需要200MB
            logger.warning(f"可用内存可能不足: {available_memory:.1f}MB，建议减少进程数")

        # 收集所有任务
        tasks = []
        for root, dirs, _ in os.walk(folder_path):
            for name in dirs:
                source_path = os.path.join(root, name)
                result_path = os.path.join(save_path, f'{name}_汇总.xlsx')
                tasks.append((source_path, result_path, data, config))

        if not tasks:
            logger.warning("没有找到需要处理的子文件夹")
            return

        logger.info(f"找到 {len(tasks)} 个处理任务，使用 {process_count} 个进程")

        # 使用增强的进程池管理器
        with ProcessPoolManager(config) as pool_manager:
            pool_manager.create_pool(process_count)

            # 提交所有任务
            results = []
            for task in tasks:
                try:
                    result = pool_manager.submit_task(merge_xlsx_files, *task)
                    results.append(result)
                except Exception as e:
                    logger.error(f"提交任务失败: {e}")

            # 等待所有任务完成
            logger.info("等待所有任务完成...")
            pool_manager.wait_for_completion()

            # 检查结果
            failed_tasks = 0
            for i, result in enumerate(results):
                try:
                    result.get(timeout=1)  # 获取结果，检查是否有异常
                except Exception as e:
                    failed_tasks += 1
                    logger.error(f"任务 {i+1} 执行失败: {e}")

            success_rate = (len(results) - failed_tasks) / len(results) if results else 0
            logger.info(f"任务完成统计: 成功 {len(results) - failed_tasks}/{len(results)} ({success_rate:.1%})")

            if failed_tasks > 0:
                logger.warning(f"有 {failed_tasks} 个任务失败，请检查日志")

    except KeyboardInterrupt:
        logger.warning("用户中断操作")
        raise
    except Exception as e:
        logger.error(f"遍历文件夹时发生错误: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise
    finally:
        # 停止资源监控
        monitor.stop_monitoring()

        # 显示统计信息
        stats = recovery.get_statistics()
        logger.info(f"容灾统计: 总任务 {stats['total_tasks']}, 完成 {stats['completed_tasks']}, 完成率 {stats['completion_rate']:.1%}")

def setup_signal_handlers():
    """设置信号处理器，用于优雅关闭"""
    def signal_handler(signum, _):
        logger.info(f"接收到信号 {signum}，正在优雅关闭...")
        # 这里可以添加清理逻辑
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def main():
    """主函数，带完整容灾机制"""
    # 设置信号处理器
    setup_signal_handlers()

    # 初始化配置和容灾系统
    config = ConfigManager()
    recovery = DisasterRecovery(config_manager=config)
    monitor = ResourceMonitor(config)

    try:
        logger.info("=" * 60)
        logger.info("Excel文件合并工具启动 - 增强容灾版本")
        logger.info("=" * 60)

        # 显示系统信息
        memory_info = psutil.virtual_memory()
        disk_info = psutil.disk_usage('.')
        logger.info(f"系统内存: {memory_info.total / (1024**3):.1f}GB (可用: {memory_info.available / (1024**3):.1f}GB)")
        logger.info(f"磁盘空间: {disk_info.total / (1024**3):.1f}GB (可用: {disk_info.free / (1024**3):.1f}GB)")

        # 清理旧的检查点数据
        recovery.cleanup_old_checkpoints(days=7)

        # 获取用户输入
        source_path = input("请输入要遍历的文件夹地址：").strip()
        if not source_path or not os.path.exists(source_path):
            logger.error("源文件夹路径无效或不存在")
            return

        save_path = input("请输入要保存的文件夹地址：").strip()
        if not save_path:
            logger.error("保存路径不能为空")
            return

        # 确保保存目录存在
        os.makedirs(save_path, exist_ok=True)

        # 检查磁盘空间
        disk_usage, is_critical = monitor.check_disk_space(save_path)
        if is_critical:
            logger.error(f"保存目录磁盘空间不足: {disk_usage:.1f}%")
            return

        logger.info(f"源路径: {source_path}")
        logger.info(f"保存路径: {save_path}")

        # 加载数据字典
        logger.info("正在加载数据字典...")
        data = dict_sj()

        if not data:
            logger.error("数据字典加载失败，无法继续")
            return

        # 开始处理
        start_time = time.time()
        traverse_folders(source_path, save_path, data)
        end_time = time.time()

        # 显示完成信息
        elapsed_time = end_time - start_time
        logger.info("=" * 60)
        logger.info(f"处理完成！总耗时: {elapsed_time:.2f}秒")

        # 显示最终统计
        stats = recovery.get_statistics()
        logger.info(f"最终统计: 总任务 {stats['total_tasks']}, 完成 {stats['completed_tasks']}, 完成率 {stats['completion_rate']:.1%}")

        if stats['pending_tasks'] > 0:
            logger.warning(f"还有 {stats['pending_tasks']} 个任务未完成，可以重新运行程序继续处理")

        logger.info("=" * 60)

    except KeyboardInterrupt:
        logger.warning("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
    finally:
        # 清理资源
        try:
            monitor.stop_monitoring()
        except:
            pass

        input("按任意键退出...")

if __name__ == '__main__':
    main()

