import os
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.styles import numbers
from openpyxl.formatting.rule import DataBarRule
from multiprocessing import Pool
from decimal import Decimal, getcontext
import time
import shutil
import logging
import traceback
from pathlib import Path
import json
from datetime import datetime


# 配置日志系统
def setup_logging():
    """设置日志配置"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    log_filename = log_dir / f"hzhz_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# 全局日志对象
logger = setup_logging()

class DisasterRecovery:
    """容灾恢复类"""

    def __init__(self, checkpoint_file="checkpoint.json"):
        self.checkpoint_file = Path(checkpoint_file)
        self.checkpoint_data = self.load_checkpoint()

    def load_checkpoint(self):
        """加载检查点数据"""
        if self.checkpoint_file.exists():
            try:
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"加载检查点失败: {e}")
        return {}

    def save_checkpoint(self, data):
        """保存检查点数据"""
        try:
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存检查点失败: {e}")

    def is_completed(self, task_id):
        """检查任务是否已完成"""
        return self.checkpoint_data.get(task_id, {}).get('completed', False)

    def mark_completed(self, task_id, result_path):
        """标记任务完成"""
        self.checkpoint_data[task_id] = {
            'completed': True,
            'result_path': str(result_path),
            'timestamp': datetime.now().isoformat()
        }
        self.save_checkpoint(self.checkpoint_data)

def retry_on_failure(max_retries=3, delay=1, backoff=2):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            retries = 0
            current_delay = delay

            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    if retries >= max_retries:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}")
                        raise

                    logger.warning(f"函数 {func.__name__} 第 {retries} 次失败，{current_delay}秒后重试: {e}")
                    time.sleep(current_delay)
                    current_delay *= backoff

            return None
        return wrapper
    return decorator

def safe_file_operation(operation, *args, **kwargs):
    """安全的文件操作"""
    temp_file = None
    try:
        if operation.__name__ in ['save', 'to_csv']:
            # 对于保存操作，使用临时文件
            original_path = args[0] if args else kwargs.get('filename', kwargs.get('path'))
            if original_path:
                temp_file = f"{original_path}.tmp"
                if args:
                    args = (temp_file,) + args[1:]
                else:
                    kwargs['filename'] = temp_file

        result = operation(*args, **kwargs)

        # 如果使用了临时文件，移动到最终位置
        if temp_file and os.path.exists(temp_file):
            final_path = temp_file[:-4]  # 移除 .tmp 后缀
            shutil.move(temp_file, final_path)
            logger.info(f"文件安全保存到: {final_path}")

        return result

    except Exception as e:
        # 清理临时文件
        if temp_file and os.path.exists(temp_file):
            try:
                os.remove(temp_file)
            except:
                pass
        logger.error(f"文件操作失败: {e}")
        raise

@retry_on_failure(max_retries=3, delay=2)
def dict_sj():
    try:
        logger.info("开始加载数据字典文件: 工作簿12.xlsx")
        workbook = openpyxl.load_workbook(filename='工作簿12.xlsx')
        # 获取默认工作表
        sheet = workbook['Sheet1']
        # 将数据转换为字典
        data = {}
        row_count = 0
        for row in sheet.iter_rows(min_row=2, values_only=True):
            if row[0] is not None:  # 确保键不为空
                key = str(row[0]).lower()  # 转换为字符串并小写
                value = row[1:]
                data[key] = value
                row_count += 1

        logger.info(f"成功加载数据字典，共 {row_count} 条记录")

        # 测试数据访问
        test_key = 'hz_ZJ_775'.lower()
        if test_key in data:
            logger.info(f"测试数据访问成功: {test_key} -> {data[test_key]}")
        else:
            logger.warning(f"测试键 {test_key} 不存在于数据字典中")

        return data

    except FileNotFoundError:
        logger.error("数据字典文件 '工作簿12.xlsx' 未找到")
        raise
    except Exception as e:
        logger.error(f"加载数据字典时发生错误: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise
def format_excel(ws):
    # 设置字体和颜色,文本对齐方式
    header_font = Font(name='Microsoft YaHei UI', size=10, bold=True, color='FFFFFF')
    content_font = Font(name='Microsoft YaHei UI', size=10)
    header_fill = PatternFill(start_color='1f4e78', end_color='1f4e78', fill_type='solid')
    content_fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')
    highlight_fill = PatternFill(start_color='f8cbad', end_color='f8cbad', fill_type='solid')

    # 格式化表头
    for row in ws.iter_rows(min_row=1, max_row=1):
        for cell in row:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    # 添加自动筛选
    ws.auto_filter.ref = ws.dimensions
    # 格式化内容
    for row in ws.iter_rows(min_row=2):
        for cell in row:
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=False)
            cell.font = content_font
            cell.fill = content_fill
    # 设置行高和列宽
    ws.row_dimensions[1].height = 35  # 表头行
    for r in range(2, ws.max_row):    # 内容行
        ws.row_dimensions[r].height = 25
    # 优化列宽
    dict_width = {'A': 8, 'B': 8, 'C': 20, 'D': 20, 'E': 50, 'F': 50, 'G': 12,'H':12}
    # 格式化各列
    for col in ws.columns:
        column = col[0].column_letter
        for i, cell in enumerate(col):
            if i > 0 and (column == 'A'):  # 序号列
                cell.font = Font(name='Microsoft YaHei UI', size=10, color='0000ff', underline='single')
            elif i > 0 and (column == 'F' or column == 'E' ):  # 问题和规则依据列
                cell.alignment = Alignment(horizontal='left', vertical='center', wrap_text=False)
            elif i > 0 and (column in ['H', 'G']):  # 金额列
                #cell.number_format = numbers.FORMAT_NUMBER_COMMA_SEPARATED1
                cell.fill = PatternFill(start_color='fce4d6', end_color='fce4d6', fill_type='solid')
        ws.column_dimensions[column].width = dict_width.get(column, 12)  # 默认宽度12
    # 设置边框
    thin_border = Border(left=Side(style='thin', color='7F7F7F'),
                        right=Side(style='thin', color='7F7F7F'),
                        top=Side(style='thin', color='7F7F7F'),
                        bottom=Side(style='thin', color='7F7F7F'))
    # 应用边框
    for row in ws.rows:
        for cell in row:
            cell.border = thin_border

    # 处理最后一行
    ws.merge_cells(start_row=ws.max_row, start_column=2, end_row=ws.max_row, end_column=5)
    for cell in ws[ws.max_row]:
        cell.font = Font(bold=True, name='Microsoft YaHei UI', size=10)
        cell.fill = highlight_fill
    ws.cell(row=ws.max_row, column=4).alignment = Alignment(horizontal='center', vertical='center')
    ws.row_dimensions[ws.max_row].height = 40

    # 冻结首行
    ws.freeze_panes = 'A2'


    return ws

@retry_on_failure(max_retries=2, delay=3)
def merge_xlsx_files(source_folder, target_file, data):
    """合并Excel文件，带容灾机制"""
    try:
        logger.info(f"开始处理文件夹: {source_folder}")

        # 检查源文件夹是否存在
        if not os.path.exists(source_folder):
            logger.warning(f"源文件夹不存在: {source_folder}")
            return

        # 检查文件夹中是否存在文件
        files_in_folder = os.listdir(source_folder)
        if not files_in_folder:
            logger.warning(f"{source_folder}中没有文件！跳过")
            return

        # 检查是否已经完成过这个任务
        task_id = f"{os.path.basename(source_folder)}_merge"
        recovery = DisasterRecovery()
        if recovery.is_completed(task_id):
            logger.info(f"任务 {task_id} 已完成，跳过")
            return

        logger.info(f"找到 {len([f for f in files_in_folder if f.lower().endswith('.xlsx')])} 个Excel文件")

        # 创建一个新工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = '汇总'
        ws.append(['序号','分类', '违规类型', '问题项目', '问题情形描述','认定依据','人次','金额'])

        # 遍历源文件夹中的所有文件
        sheet_num = 1
        processed_files = 0
        failed_files = []

        for filename in os.listdir(source_folder):
            if filename.lower().endswith(".xlsx"):
                file_path = os.path.join(source_folder, filename)
                logger.info(f"正在处理文件: {filename}")

                try:
                    workbook = openpyxl.load_workbook(file_path, read_only=True)
                    processed_files += 1
                except Exception as e:
                    logger.warning(f"无法加载文件 {file_path}: {e}")
                    failed_files.append(filename)
                    continue
            # 遍历工作簿中的所有工作表
            for sheetname in workbook.sheetnames:
                # 复制工作表到新工作簿中的单独工作表中
                sheet = workbook[sheetname]
                new_ws = wb.create_sheet(f"{sheet_num}")
                new_ws.title = f"{sheet_num}"
                # 遍历单元格，将值复制到新的单元格中
                for row in sheet.iter_rows():
                    new_ws.append([cell.value for cell in row])
                last_col = new_ws.max_column
                second_last_col = last_col - 1
                total_violations = 0
                total_amount = 0
                # for col in new_ws.iter_cols(min_col=second_last_col, max_col=last_col):
                #     for cell in col:
                #         if cell.row == 1:
                #             continue
                #         if cell.column == second_last_col:
                #             if cell.value is not None:
                #              try:
                #                 total_violations += float(cell.value)
                #                 cell.value = float(cell.value)
                #                 #设置为数字
                #              except ValueError:
                #                  total_violations = 0
                #                  total_amount = 0
                #                  print(cell.value)
                #                  print('最后不是数字:'+ os.path.join(source_folder, filename))
                #                  break
                #         elif cell.column == last_col:
                #             if cell.value is not None:
                #              try:
                #                  # 设置为数字
                #                  total_amount += float(cell.value)
                #                  cell.value = float(cell.value)
                #              except ValueError:
                #                  total_violations = 0
                #                  total_amount = 0
                #                  print(cell.value)
                #                  print('最后不是数字:'+ os.path.join(source_folder, filename))
                #                  break
                # 查找money_rules和bill_id列
                money_col = None
                bill_id_col = None
                for col in new_ws.iter_cols(min_row=1, max_row=1):  # 只在表头行查找
                    for cell in col:
                        if cell.value == "违规金额":
                            money_col = cell.column
                        elif cell.value == "单据号":
                            bill_id_col = cell.column
                        #找不到违规金额取最后一列
                    if money_col is None:
                        money_col = last_col

                getcontext().prec = 10  # 设置足够高的精度

                total_amount = Decimal('0')
                distinct_count = 0
                bill_ids = set()
                if money_col is not None and bill_id_col is not None:
                    for row in new_ws.iter_rows(min_row=2):
                        # 处理金额列
                        money_cell = row[money_col-1]
                        if money_cell.value is not None and str(money_cell.value).strip():
                            try:
                                amount = Decimal(str(money_cell.value).strip())
                                total_amount += amount
                            except (ValueError, TypeError) as e:
                                print(f'金额列包含无效数据，文件: {os.path.join(source_folder, filename)}，行: {row[0].row}，值: {money_cell.value}，错误: {str(e)}')

                        # 处理ID列
                        bill_cell = row[bill_id_col-1]
                        if bill_cell.value is not None and str(bill_cell.value).strip():
                            bill_ids.add(str(bill_cell.value).strip())

                    distinct_count = len(bill_ids)
                elif money_col is not None:  # 只有金额列的情况
                    for row in new_ws.iter_rows(min_row=2):
                        money_cell = row[money_col-1]
                        if money_cell.value is not None and str(money_cell.value).strip():
                            try:
                                amount = Decimal(str(money_cell.value).strip())
                                total_amount += amount
                            except (ValueError, TypeError) as e:
                                print(f'金额列包含无效数据，文件: {os.path.join(source_folder, filename)}，行: {row[0].row}，值: {money_cell.value}，错误: {str(e)}')
                elif bill_id_col is not None:  # 只有ID列的情况
                    bill_ids = set()
                    for row in new_ws.iter_rows(min_row=2):
                        bill_cell = row[bill_id_col-1]
                        if bill_cell.value is not None:
                            bill_ids.add(str(bill_cell.value))
                    distinct_count = len(bill_ids)

                #(['序号', '违规类型', '问题项目', '问题情形描述','认定依据','人次','金额'])
                gzmc = filename[:-5].lower()
                if data.get(gzmc, ['','','','','',''])[6].lower() == 'd':
                    total_amount = '\\N'
                    distinct_count = '\\N'
                ws.append([f'=HYPERLINK("#{sheet_num}!A1", "{sheet_num}")',
                            data.get(gzmc, ['','','','','',''])[6],
                          data.get(gzmc, ['',gzmc,'检查字典表','','',''])[1],
                          data.get(gzmc, ['','','','','',''])[2],
                          data.get(gzmc, ['','','','','',''])[4],
                          data.get(gzmc, ['','','','','',''])[5],
                          distinct_count,
                          total_amount])
                sheet_num += 1

        # 保存新工作簿
        ws.append(['合计', f'共计-{ws.max_row - 1}-条违规项目', '', f'', '',
                  f'=SUM(G2:G{ws.max_row})',  # 人次总和
                  f'=SUM(H2:H{ws.max_row})'])  # 金额总和
        ws = format_excel(ws)
        wb.properties.creator="飞"

        # 使用安全文件操作保存
        safe_file_operation(wb.save, target_file)

        # 记录处理结果
        logger.info(f"成功处理 {processed_files} 个文件，失败 {len(failed_files)} 个文件")
        if failed_files:
            logger.warning(f"失败的文件: {failed_files}")

        # 标记任务完成
        recovery.mark_completed(task_id, target_file)
        logger.info(f"文件合并完成: {target_file}")

    except Exception as e:
        logger.error(f"合并文件时发生错误: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise
def traverse_folders(folder_path, save_path, data):
    # 获取CPU核心数作为默认进程数
    process_count = int(input(f"几个进程:"))

    # 收集所有任务
    tasks = []
    for root, dirs, files in os.walk(folder_path):
        for name in dirs:
            source_path = os.path.join(root, name)
            result_path = os.path.join(save_path, f'{name}_汇总.xlsx')
            tasks.append((source_path, result_path, data))

    # 使用进程池并行处理
    with Pool(processes=process_count) as pool:
        pool.starmap(merge_xlsx_files, tasks)

if __name__ == '__main__':
    source_path = input("请输入要遍历的文件夹地址：")
    save_path = input("请输入要保存的文件夹地址：")
    data = dict_sj() # 获取数据字典
    traverse_folders(source_path, save_path,data) # 调用多进程遍历子目录并合并Excel文件的函数
    input("输入任意键退出：")

