rule_name,sql,count
遂昌县湖山中心卫生院,"SELECT * FROM hz_yb_fymxxx_20250422 where medical_code = 'H33112300064' AND (ITEM_code IN ('43000000112','43000000113','43000001604','43000000100','43000001002','43000001603','43000001000','43000000200','43000001601','43000001600')  OR  ITEM_CODE LIKE 'T%')

 AND  YEAR(clear_time) >=2024",45523
遂昌县湖山中心卫生院,"SELECT * FROM hz_yb_fymxxx_20250422 where medical_code = 'H33112300064' AND (ITEM_code IN ('43000000112','43000000113','43000001604','43000000100','43000001002','43000001603','43000001000','43000000200','43000001601','43000001600')  OR  ITEM_CODE LIKE 'T%')

 AND  YEAR(clear_time) >=2024",45523
遂昌县湖山中心卫生院(2023),"SELECT * FROM hz_yb_fymxxx_20250422 where medical_code = 'H33112300064' AND (ITEM_code IN ('43000000112','43000000113','43000001604','43000000100','43000001002','43000001603','43000001000','43000000200','43000001601','43000001600') )

 AND  YEAR(clear_time) = 2023",1316
头孢克肟,"SELECT * FROM hz_yb_fymxxx_20250422 where medical_name = '松阳一涵诊所' AND ITEM_NAME LIKE '头孢克肟%'

 AND  YEAR(clear_time) >= 2023",180
金额大于500," select a.*,b.医疗费用总额,b.个人账户支出,B.基金支付总额,基金医疗保险统筹基金支出 FROM
 hz_yb_fyjsxx_20250422 b
 join   hz_yb_fymxxx_20250422 a
 on b.结算id = a.BILL_ID  and b.医疗费用总额 > 500 AND a.medical_code like 'P331125%'",12267
凝血因子VIII,"select * from hz_yb_fymxxx where area_code = '331102' and item_name in ('人凝血因子Ⅷ','注射用重组人凝血因子Ⅷ') and year(clear_time) = 2024",174
凝血因子VIII,"select * from hz_yb_fymxxx where area_code = '331102' and item_name in ('人凝血因子Ⅷ','注射用重组人凝血因子Ⅷ') and year(clear_time) > 2023",244
凝血因子VIII,"select * from hz_yb_fymxxx where area_code = '331102' and item_name in ('人凝血因子Ⅷ','注射用重组人凝血因子Ⅷ') and year(clear_time) >= 2023",507
浙江启正大药房有限公司遂昌云峰店," select * from hz_yb_fymxxx where medical_name  = '浙江启正大药房有限公司遂昌云峰店' AND year(clear_time) >= 2024",17484
奥希替尼," select * from hz_yb_fymxxx where year(clear_time) = 2022 and item_code = 'XL01XEA298A001020279096'",0
明细,"select * from hz_yb_fymxxx where medical_name in ('遂昌县焦滩乡蔡口村卫生室','遂昌县云峰街道连头村卫生室','遂昌县北界镇淤弓村卫生室','遂昌县垵口乡垵口村卫生所','遂昌县金竹镇王村村卫生室') and year(clear_time) >=2023",37789
1腰部疾病推拿治疗,"select * from hz_yb_fymxxx t where t.item_code='45000000600'
and t.pay_per_retio<>1
and t.in_diagnose_name||t.out_diagnose_name not like '%腰椎间盘突出%'",13928
腰部疾病推拿治疗,"select * from hz_yb_fymxxx t where t.item_code='45000000600'",157878
缙云鸿福康复医院,"select * from hz_yb_fymxxx where MEDICAL_CODE in ('H33112200070','302F4284DEB')",1494115
缙云鸿福康复医院,"select * from hz_yb_fymxxx where MEDICAL_CODE = 'H33112200070'
order by clear_time,BILL_ID,BILL_DETAIL_ID desc LIMIT 200000",200000
缙云鸿福康复医院住院明细,"select * from hz_yb_fymxxx where MEDICAL_CODE = 'H33112200070' and MEDICAL_MODE = '普通住院'
order by clear_time,BILL_ID,BILL_DETAIL_ID  desc LIMIT 200000",200000
缙云鸿福康复医院住院明细,"select * from hz_yb_fymxxx where MEDICAL_CODE = 'H33112200070' and MEDICAL_MODE = '普通住院'
order by clear_time,BILL_ID,BILL_DETAIL_ID   LIMIT 200000",200000
缙云鸿福康复医院(24.02-24.10),"select * from hz_yb_fymxxx where MEDICAL_CODE = 'H33112200070' and to_char(clear_time,'YYYY-MM') BETWEEN '2024-02' and '2024-10'",298211
“泪道冲洗”重复收取“泪小点扩张”,"select t.*,
       (case
         when t.item_name like '泪小点扩张%' then
          t.money
       end) money_rules
  from hz_yb_fymxxx t
 where exists (select 1
          from hz_yb_fymxxx a
         where a.pay_per_retio < 1
           and (a.item_name in ('泪道冲洗'))
           and t.bill_id = a.bill_id
     and datetrunc(t.cost_time,'dd') = datetrunc(a.cost_time,'dd'))
   and exists (select 1
          from hz_yb_fymxxx a
         where (a.item_name LIKE '泪小点扩张%' )
     and a.item_code not in  ('泪道冲洗')
           and t.bill_id = a.bill_id
           and datetrunc(t.cost_time,'dd') = datetrunc(a.cost_time,'dd')
           and t.medical_code = a.medical_code  )
   and ((t.item_name in ('泪道冲洗') and
       t.pay_per_retio < 1  ) or t.item_name LIKE '泪小点扩张%' )",168512
“泪道冲洗”重复“泪小点扩张”,"SELECT *,(case
         when t.item_name like '泪小点扩张%' then
          t.money
       end) money_rules
FROM hz_yb_fymxxx t
WHERE t.bill_id IN (
    SELECT bill_id
    FROM hz_yb_fymxxx
    WHERE pay_per_retio < 1
    AND item_name = '泪道冲洗'
)
AND t.bill_id IN (
    SELECT bill_id
    FROM hz_yb_fymxxx
    WHERE item_name LIKE '泪小点扩张%'
    AND item_name != '泪道冲洗'
    AND medical_code = t.medical_code
    AND datetrunc(cost_time,'dd') = datetrunc(t.cost_time,'dd')
)
AND (
    (t.item_name = '泪道冲洗' AND t.pay_per_retio < 1)
    OR t.item_name LIKE '泪小点扩张%'
)",176634
为60岁以上患者普遍开展性激素检查,"select *,GET_IDCARD_AGE(card_id) as 年龄 from hz_yb_fymxxx where GET_IDCARD_AGE(card_id) > 60 AND  item_name in ('血清促卵泡刺激素测定','血清促黄体生成素测定')",10712
流感病毒抗体测（IgM）同一分钟内收费大于1次,"select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd hh:mi:ss') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
wm_concat(distinct ',',t.unit_price) 单价 ,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额,
sum(t.num)-1 超收数量,
(sum(t.num)-1)*min(t.unit_price) 超收金额
from hz_yb_fymxxx t
where  t.item_code in ('25040302803')
and (to_char(t.cost_time, 'hh24')<> '00' or
to_char(t.cost_time, 'mi')<>'00' or
to_char(t.cost_time, 'ss')<>'00')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd hh:mi:ss'),
t.in_time,
t.out_time
having sum(t.num)>1",33191
流感病毒抗体测（IgM）同一分钟内收费大于1次,"select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd hh:mi:ss') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
wm_concat(distinct ',',t.unit_price) 单价 ,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额,
sum(t.num)-1 超收数量,
(sum(t.num)-1)*min(t.unit_price) 超收金额
from hz_yb_fymxxx t
where  t.item_code in ('25040302803')
and (to_char(t.cost_time, 'hh24')<> '00' or
to_char(t.cost_time, 'mi')<>'00' or
to_char(t.cost_time, 'ss')<>'00')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd hh:mi:ss'),
t.in_time,
t.out_time
having sum(t.num)>1",33191
同进同出主单,"

SELECT * FROM hz_yb_fyjsxx  WHERE (定点机构名称,定点机构编码,开始时间 || '到' || 结束时间) IN  (
select 定点机构名称,定点机构编码,开始时间 || '到' || 结束时间 from hz_yb_fyjsxx  WHERE 定点机构编码 in ('H33112200029',
'H33112200006',
'H33112600717',
'H33110200424',
'H33112500295',
'H33112700035',
'H33112200686',
'H33112400265',
'H33112200070',
'H33112100073')  and 医疗类别 like '%住院%'
GROUP BY 定点机构名称,定点机构编码,开始时间 || '到' || 结束时间
HAVING regexp_count(wm_concat(',',姓名),',') > 0
ORDER BY  定点机构名称,定点机构编码,开始时间 || '到' || 结束时间)  and 医疗类别 like '%住院%'",44123
乙肝病毒外膜蛋白前S抗体测定—不规范诊疗,"select * from hz_yb_fymxxx t where t.item_code in ('25040301101','25040301201')
and t.pay_per_retio<>1
and t.in_diagnose_name||t.out_diagnose_name not like '%肝%'",2698
乙肝病毒外膜蛋白前S抗原测定—不规范诊疗,"select * from hz_yb_fymxxx t where t.item_code in ('25040301100','25040301200')
and t.pay_per_retio<>1
and t.in_diagnose_name||t.out_diagnose_name not like '%肝%'",4263
峰源乡卫生院螺内酯 呋塞米,"select * from hz_yb_fymxxx where medical_code = 'H33110200298' AND (item_NAME LIKE '螺内酯%' OR item_NAME LIKE '呋塞米%')",308
药店金额大于1000," select a.*,b.医疗费用总额,b.个人账户支出,B.基金支付总额,基金医疗保险统筹基金支出 FROM
 hz_yb_fyjsxx b
 join   hz_yb_fymxxx a
 on b.结算id = a.BILL_ID  and b.医疗费用总额 > 1000  and b.定点机构编码 like 'P3311%'",390734
数字影像服务费,"SELECT * FROM TEMP_szyxfwf ",1762347
数字影像服务费,"SELECT * FROM TEMP_szyxfwf ",1762347
数字影像服务费,"SELECT * FROM TEMP_szyxfwf ",1762347
夜间刷卡明细,"select * from hz_yb_fyjsxx where 定点机构编码 in ('P33118100116','P33118100140','P33110200012','P33112100350','P33112200072') AND ((to_char(结算时间,'hh:mi:ss') BETWEEN '00:00:00' and '07:29:59') OR to_char(结算时间,'hh') >= '21')
",20942
夜间刷卡明细,"select * from hz_yb_fymxxx where medical_code in ('P33118100116','P33118100140','P33110200012','P33112100350','P33112200072') AND ((to_char(clear_time,'hh:mi:ss') BETWEEN '00:00:00' and '07:29:59') OR to_char(clear_time,'hh') >= '21')
",36260
夜间刷卡明细,"select a.*,b.余额
from hz_yb_fymxxx a
join hz_yb_fyjsxx b
on a.bill_id = b.结算id
where b.定点机构编码 in ('P33118100116','P33118100140','P33110200012','P33112100350','P33112200072')
AND ((to_char(b.结算时间,'hh:mi:ss')
BETWEEN '00:00:00' and '07:29:59') OR to_char(b.结算时间,'hh') >= '21')",36257
夜间刷卡明细,"select a.*,b.余额
from hz_yb_fymxxx a
LEFT join hz_yb_fyjsxx b
on a.bill_id = b.结算id
where A.medical_code in ('P33118100116','P33118100140','P33110200012','P33112100350','P33112200072')
AND ((to_char(A.clear_time,'hh:mi:ss')
BETWEEN '00:00:00' and '07:29:59') OR to_char(A.clear_time,'hh') >= '21')",36260
医保项目名称“经皮静脉%"、”经皮超选择性动脉造影术“（32020000300）同时收取”经皮血管瘤腔内药物灌注术“（32020001300）,"select t.*,
       (case
         when t.item_name like '经皮血管瘤腔内药物灌注术' then
          t.money
       end) money_rules
  from hz_yb_fymxxx t
 where exists (select 1
          from hz_yb_fymxxx a
         where a.pay_per_retio < 1
           and a.item_name = '经皮血管瘤腔内药物灌注术' -- 胸腔闭式引流术
           and t.bill_id = a.bill_id
     and datetrunc(t.cost_time,'dd') = datetrunc(a.cost_time,'dd')
           and t.medical_code = a.medical_code)
   and exists (select 1
          from hz_yb_fymxxx a
         where (a.item_code like '经皮静脉%' or a.item_code like '经皮超选择性动脉造影术')
     and a.item_name != '经皮血管瘤腔内药物灌注术'
           and t.bill_id = a.bill_id
           and datetrunc(t.cost_time,'dd') = datetrunc(a.cost_time,'dd')
           and t.medical_code = a.medical_code)
   and ((t.item_name like '经皮血管瘤腔内药物灌注术' and
       t.pay_per_retio < 1) or (t.item_code like '经皮静脉%' or t.item_code like '经皮超选择性动脉造影术'))
",0
非精神科患者收取麻醉分析,"select * from hz_yb_fymxxx where medical_code like '_3311%' AND (item_NAME LIKE '麻醉分析' ) and DEPT_NAME || DISCHARGE_DEPT_NAME LIKE '%精神%'",0
