#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Nuitka打包hzhz.py的脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_nuitka():
    """检查Nuitka是否已安装"""
    try:
        result = subprocess.run(['nuitka', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Nuitka已安装: {result.stdout.strip()}")
            return True
        else:
            print("✗ Nuitka未正确安装")
            return False
    except FileNotFoundError:
        print("✗ Nuitka未安装")
        return False

def install_nuitka():
    """安装Nuitka"""
    print("正在安装Nuitka...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'nuitka'], check=True)
        print("✓ Nuitka安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Nuitka安装失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    dependencies = ['openpyxl', 'psutil']
    missing = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✓ {dep} 已安装")
        except ImportError:
            missing.append(dep)
            print(f"✗ {dep} 未安装")
    
    if missing:
        print(f"正在安装缺失的依赖: {', '.join(missing)}")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install'] + missing, check=True)
            print("✓ 依赖安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ 依赖安装失败: {e}")
            return False
    
    return True

def build_executable():
    """使用Nuitka构建可执行文件"""
    print("开始使用Nuitka构建可执行文件...")
    
    # 构建命令
    cmd = [
        'nuitka',
        '--standalone',  # 独立模式，包含所有依赖
        '--onefile',     # 单文件模式
        '--output-filename=hzhz.exe',  # 输出文件名
        '--output-dir=dist',  # 输出目录
        '--remove-output',    # 构建前清理输出目录
        '--assume-yes-for-downloads',  # 自动下载依赖
        '--plugin-enable=multiprocessing',  # 启用多进程插件
        '--windows-console-mode=force',  # Windows控制台模式
        '--include-data-dir=logs=logs',  # 包含日志目录
        '--include-data-file=disaster_config.ini=disaster_config.ini',  # 包含配置文件
        '--include-data-file=工作簿12.xlsx=工作簿12.xlsx',  # 包含数据字典文件（如果存在）
        'hzhz.py'  # 源文件
    ]
    
    # 检查数据字典文件是否存在
    if not os.path.exists('工作簿12.xlsx'):
        print("⚠️  警告: 工作簿12.xlsx 不存在，将从构建中排除")
        cmd = [c for c in cmd if not c.startswith('--include-data-file=工作簿12.xlsx')]
    
    # 检查配置文件是否存在
    if not os.path.exists('disaster_config.ini'):
        print("⚠️  警告: disaster_config.ini 不存在，将从构建中排除")
        cmd = [c for c in cmd if not c.startswith('--include-data-file=disaster_config.ini')]
    
    print(f"构建命令: {' '.join(cmd)}")
    
    try:
        # 创建输出目录
        os.makedirs('dist', exist_ok=True)
        
        # 执行构建
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 构建成功完成")
        
        # 检查输出文件
        exe_path = Path('dist/hzhz.exe')
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"✓ 可执行文件已生成: {exe_path} ({size_mb:.1f}MB)")
            return True
        else:
            print("✗ 可执行文件未找到")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_build_script():
    """创建批处理构建脚本"""
    batch_content = """@echo off
echo 使用Nuitka构建hzhz.exe
echo ========================

REM 检查Python
python --version
if errorlevel 1 (
    echo 错误: Python未安装或未添加到PATH
    pause
    exit /b 1
)

REM 安装Nuitka（如果未安装）
python -m pip install nuitka

REM 安装依赖
python -m pip install openpyxl psutil

REM 构建可执行文件
nuitka --standalone --onefile --output-filename=hzhz.exe --output-dir=dist --remove-output --assume-yes-for-downloads --plugin-enable=multiprocessing --windows-console-mode=force hzhz.py

echo.
echo 构建完成！
echo 可执行文件位置: dist\\hzhz.exe
pause
"""
    
    with open('build.bat', 'w', encoding='gbk') as f:
        f.write(batch_content)
    
    print("✓ 已创建 build.bat 批处理脚本")

def create_requirements_for_build():
    """创建构建专用的requirements文件"""
    build_requirements = """# 构建依赖
nuitka>=1.8.0

# 运行时依赖
openpyxl>=3.0.0
psutil>=5.8.0

# 可选的构建优化依赖
# zstandard  # 压缩优化
# ordered-set  # 构建优化
"""
    
    with open('requirements-build.txt', 'w', encoding='utf-8') as f:
        f.write(build_requirements)
    
    print("✓ 已创建 requirements-build.txt")

def main():
    """主函数"""
    print("=" * 60)
    print("Nuitka 打包工具 - hzhz.py")
    print("=" * 60)
    
    # 检查源文件
    if not os.path.exists('hzhz.py'):
        print("✗ 错误: hzhz.py 文件不存在")
        return False
    
    print("✓ 源文件 hzhz.py 存在")
    
    # 创建辅助文件
    create_build_script()
    create_requirements_for_build()
    
    # 检查并安装Nuitka
    if not check_nuitka():
        if not install_nuitka():
            print("无法安装Nuitka，请手动安装: pip install nuitka")
            return False
    
    # 检查依赖
    if not check_dependencies():
        print("依赖检查失败")
        return False
    
    # 构建可执行文件
    if build_executable():
        print("\n" + "=" * 60)
        print("✓ 打包成功完成！")
        print("可执行文件位置: dist/hzhz.exe")
        print("=" * 60)
        
        # 提供使用说明
        print("\n使用说明:")
        print("1. 将 dist/hzhz.exe 复制到目标机器")
        print("2. 确保目标机器有 工作簿12.xlsx 数据字典文件")
        print("3. 直接双击运行 hzhz.exe")
        print("4. 或在命令行中运行: hzhz.exe")
        
        return True
    else:
        print("\n" + "=" * 60)
        print("✗ 打包失败")
        print("请检查错误信息并重试")
        print("=" * 60)
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
