2025-05-27 10:09:03,226 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:09:03,238 - INFO - start_monitoring:196 - 资源监控已启动
2025-05-27 10:09:03,238 - INFO - _merge_xlsx_files_impl:768 - 开始处理文件夹: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33110200399-丽水莲都严振茂诊所
2025-05-27 10:09:03,239 - INFO - _merge_xlsx_files_impl:805 - 找到 4 个Excel文件
2025-05-27 10:09:03,241 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (1/4): HZ_ZJ_649.xlsx
2025-05-27 10:09:16,051 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (2/4): HZ_ZJ_781.xlsx
2025-05-27 10:09:16,064 - WARNING - check_memory_usage:129 - 内存使用率过高: 80.9% (阈值: 80%)
2025-05-27 10:09:16,064 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:16,321 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 10
2025-05-27 10:09:16,329 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (3/4): HZ_ZJ_787.xlsx
2025-05-27 10:09:16,340 - WARNING - check_memory_usage:129 - 内存使用率过高: 80.8% (阈值: 80%)
2025-05-27 10:09:16,341 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:16,599 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:09:16,599 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (4/4): HZ_ZJ_788.xlsx
2025-05-27 10:09:16,614 - WARNING - check_memory_usage:129 - 内存使用率过高: 80.8% (阈值: 80%)
2025-05-27 10:09:16,614 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:16,899 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:09:16,914 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:09:16,916 - ERROR - safe_file_context:458 - 文件操作失败: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:09:16,916 - ERROR - _merge_xlsx_files_impl:982 - 合并文件时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:09:16,917 - ERROR - _merge_xlsx_files_impl:983 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file, config)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:09:21,931 - INFO - stop_monitoring:203 - 资源监控已停止
2025-05-27 10:09:21,931 - ERROR - merge_xlsx_files:756 - 文件合并发生不可重试的异常: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:09:21,931 - ERROR - _process_single_folder:1020 - 处理文件夹 C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33110200399-丽水莲都严振茂诊所 时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:09:21,932 - ERROR - _process_single_folder:1021 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1018, in _process_single_folder
    return merge_xlsx_files(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 740, in merge_xlsx_files
    return _merge_xlsx_files_impl(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file, config)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:09:21,934 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:09:21,934 - INFO - load_checkpoint:222 - 成功加载检查点文件: checkpoint.json
2025-05-27 10:09:21,945 - WARNING - check_memory_usage:129 - 内存使用率过高: 80.8% (阈值: 80%)
2025-05-27 10:09:21,946 - INFO - start_monitoring:196 - 资源监控已启动
2025-05-27 10:09:22,156 - INFO - _merge_xlsx_files_impl:768 - 开始处理文件夹: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33110200403-丽水万顺诊所
2025-05-27 10:09:22,503 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 2746325
2025-05-27 10:09:22,505 - INFO - _merge_xlsx_files_impl:805 - 找到 2 个Excel文件
2025-05-27 10:09:22,505 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (1/2): HZ_ZJ_788.xlsx
2025-05-27 10:09:22,525 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (2/2): 中医定向透药疗法.xlsx
2025-05-27 10:09:22,547 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:09:22,548 - ERROR - safe_file_context:458 - 文件操作失败: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:09:22,548 - ERROR - _merge_xlsx_files_impl:982 - 合并文件时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:09:22,548 - ERROR - _merge_xlsx_files_impl:983 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file, config)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:09:27,555 - INFO - stop_monitoring:203 - 资源监控已停止
2025-05-27 10:09:27,555 - ERROR - merge_xlsx_files:756 - 文件合并发生不可重试的异常: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:09:27,555 - ERROR - _process_single_folder:1020 - 处理文件夹 C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33110200403-丽水万顺诊所 时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:09:27,556 - ERROR - _process_single_folder:1021 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1018, in _process_single_folder
    return merge_xlsx_files(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 740, in merge_xlsx_files
    return _merge_xlsx_files_impl(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file, config)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:09:27,558 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:09:27,559 - INFO - load_checkpoint:222 - 成功加载检查点文件: checkpoint.json
2025-05-27 10:09:27,569 - INFO - start_monitoring:196 - 资源监控已启动
2025-05-27 10:09:27,570 - INFO - _merge_xlsx_files_impl:768 - 开始处理文件夹: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33112500431-云和县人民医院（云和县医疗健康集团人民医院院区）
2025-05-27 10:09:27,574 - INFO - _merge_xlsx_files_impl:805 - 找到 116 个Excel文件
2025-05-27 10:09:27,575 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (1/116): HZ_ZJ_007.xlsx
2025-05-27 10:09:27,589 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (2/116): HZ_ZJ_018.xlsx
2025-05-27 10:09:27,612 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (3/116): HZ_ZJ_024.xlsx
2025-05-27 10:09:27,647 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (4/116): HZ_ZJ_029.xlsx
2025-05-27 10:09:27,695 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (5/116): HZ_ZJ_038.xlsx
2025-05-27 10:09:27,709 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (6/116): HZ_ZJ_039.xlsx
2025-05-27 10:09:27,722 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (7/116): HZ_ZJ_079.xlsx
2025-05-27 10:09:27,784 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (8/116): HZ_ZJ_089.xlsx
2025-05-27 10:09:27,802 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (9/116): HZ_ZJ_235.xlsx
2025-05-27 10:09:27,825 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (10/116): HZ_ZJ_299.xlsx
2025-05-27 10:09:27,937 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (11/116): HZ_ZJ_306.xlsx
2025-05-27 10:09:27,990 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (12/116): HZ_ZJ_317.xlsx
2025-05-27 10:09:28,151 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (13/116): HZ_ZJ_322.xlsx
2025-05-27 10:09:29,169 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (14/116): HZ_ZJ_324.xlsx
2025-05-27 10:09:29,185 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (15/116): HZ_ZJ_326.xlsx
2025-05-27 10:09:37,075 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (16/116): HZ_ZJ_327.xlsx
2025-05-27 10:09:37,094 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (17/116): HZ_ZJ_332.xlsx
2025-05-27 10:09:37,119 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (18/116): HZ_ZJ_334.xlsx
2025-05-27 10:09:37,134 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (19/116): HZ_ZJ_338.xlsx
2025-05-27 10:09:38,063 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (20/116): HZ_ZJ_339.xlsx
2025-05-27 10:09:38,092 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (21/116): HZ_ZJ_340.xlsx
2025-05-27 10:09:38,116 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (22/116): HZ_ZJ_345.xlsx
2025-05-27 10:09:38,135 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (23/116): HZ_ZJ_347.xlsx
2025-05-27 10:09:38,193 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (24/116): HZ_ZJ_357.xlsx
2025-05-27 10:09:38,209 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (25/116): HZ_ZJ_359.xlsx
2025-05-27 10:09:38,233 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (26/116): HZ_ZJ_364.xlsx
2025-05-27 10:09:38,254 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (27/116): HZ_ZJ_369.xlsx
2025-05-27 10:09:38,267 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (28/116): HZ_ZJ_371.xlsx
2025-05-27 10:09:38,282 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (29/116): HZ_ZJ_373.xlsx
2025-05-27 10:09:38,298 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (30/116): HZ_ZJ_375.xlsx
2025-05-27 10:09:38,321 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (31/116): HZ_ZJ_376.xlsx
2025-05-27 10:09:38,335 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (32/116): HZ_ZJ_382.xlsx
2025-05-27 10:09:38,360 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (33/116): HZ_ZJ_394.xlsx
2025-05-27 10:09:38,379 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (34/116): HZ_ZJ_396.xlsx
2025-05-27 10:09:38,395 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (35/116): HZ_ZJ_397.xlsx
2025-05-27 10:09:38,412 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (36/116): HZ_ZJ_402.xlsx
2025-05-27 10:09:38,431 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (37/116): HZ_ZJ_405.xlsx
2025-05-27 10:09:38,443 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (38/116): HZ_ZJ_411.xlsx
2025-05-27 10:09:38,463 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (39/116): HZ_ZJ_420.xlsx
2025-05-27 10:09:38,488 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (40/116): HZ_ZJ_423.xlsx
2025-05-27 10:09:38,506 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (41/116): HZ_ZJ_426.xlsx
2025-05-27 10:09:38,522 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (42/116): HZ_ZJ_437.xlsx
2025-05-27 10:09:43,595 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (43/116): HZ_ZJ_438.xlsx
2025-05-27 10:09:43,606 - WARNING - check_memory_usage:129 - 内存使用率过高: 82.1% (阈值: 80%)
2025-05-27 10:09:43,606 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:43,894 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 10
2025-05-27 10:09:48,995 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (44/116): HZ_ZJ_440.xlsx
2025-05-27 10:09:49,006 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.5% (阈值: 80%)
2025-05-27 10:09:49,006 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:49,372 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 10
2025-05-27 10:09:51,350 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (45/116): HZ_ZJ_441.xlsx
2025-05-27 10:09:51,359 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.8% (阈值: 80%)
2025-05-27 10:09:51,360 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:51,762 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:09:51,851 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (46/116): HZ_ZJ_442.xlsx
2025-05-27 10:09:51,861 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.7% (阈值: 80%)
2025-05-27 10:09:51,862 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:52,271 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:09:52,573 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (47/116): HZ_ZJ_444.xlsx
2025-05-27 10:09:52,583 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.0% (阈值: 80%)
2025-05-27 10:09:52,583 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:53,008 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 169
2025-05-27 10:09:53,456 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (48/116): HZ_ZJ_445.xlsx
2025-05-27 10:09:53,457 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.0% (阈值: 80%)
2025-05-27 10:09:53,457 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:53,893 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:09:53,909 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (49/116): HZ_ZJ_446.xlsx
2025-05-27 10:09:53,909 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.9% (阈值: 80%)
2025-05-27 10:09:53,909 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:54,320 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:09:54,334 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (50/116): HZ_ZJ_447.xlsx
2025-05-27 10:09:54,341 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.8% (阈值: 80%)
2025-05-27 10:09:54,341 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:54,792 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:09:55,074 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (51/116): HZ_ZJ_449.xlsx
2025-05-27 10:09:55,094 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.9% (阈值: 80%)
2025-05-27 10:09:55,095 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:55,548 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:09:55,589 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (52/116): HZ_ZJ_450.xlsx
2025-05-27 10:09:55,604 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.9% (阈值: 80%)
2025-05-27 10:09:55,604 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:56,050 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:09:56,067 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (53/116): HZ_ZJ_452.xlsx
2025-05-27 10:09:56,077 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.9% (阈值: 80%)
2025-05-27 10:09:56,077 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:56,490 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:09:56,509 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (54/116): HZ_ZJ_454.xlsx
2025-05-27 10:09:56,518 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.9% (阈值: 80%)
2025-05-27 10:09:56,519 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:56,940 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:09:57,597 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.2% (阈值: 80%)
2025-05-27 10:09:58,085 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:09:58,085 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (55/116): HZ_ZJ_455.xlsx
2025-05-27 10:09:58,085 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.1% (阈值: 80%)
2025-05-27 10:09:58,085 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:58,578 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 0
2025-05-27 10:09:58,640 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (56/116): HZ_ZJ_458.xlsx
2025-05-27 10:09:58,656 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.1% (阈值: 80%)
2025-05-27 10:09:58,657 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:59,133 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:09:59,137 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (57/116): HZ_ZJ_459.xlsx
2025-05-27 10:09:59,147 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.1% (阈值: 80%)
2025-05-27 10:09:59,148 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:09:59,602 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:09:59,604 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (58/116): HZ_ZJ_477.xlsx
2025-05-27 10:09:59,613 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.1% (阈值: 80%)
2025-05-27 10:09:59,613 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:00,068 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:00,604 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (59/116): HZ_ZJ_491.xlsx
2025-05-27 10:10:00,606 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.3% (阈值: 80%)
2025-05-27 10:10:00,606 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:01,086 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:01,102 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (60/116): HZ_ZJ_497.xlsx
2025-05-27 10:10:01,110 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.3% (阈值: 80%)
2025-05-27 10:10:01,110 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:01,575 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:01,810 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (61/116): HZ_ZJ_502.xlsx
2025-05-27 10:10:01,819 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.5% (阈值: 80%)
2025-05-27 10:10:01,819 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:02,286 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:02,290 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (62/116): HZ_ZJ_510.xlsx
2025-05-27 10:10:02,299 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.4% (阈值: 80%)
2025-05-27 10:10:02,300 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:02,779 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:02,889 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (63/116): HZ_ZJ_518.xlsx
2025-05-27 10:10:02,910 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.5% (阈值: 80%)
2025-05-27 10:10:02,910 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:03,388 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:03,444 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (64/116): HZ_ZJ_521.xlsx
2025-05-27 10:10:03,455 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.5% (阈值: 80%)
2025-05-27 10:10:03,455 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:03,923 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:04,265 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (65/116): HZ_ZJ_526.xlsx
2025-05-27 10:10:04,275 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.6% (阈值: 80%)
2025-05-27 10:10:04,276 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:04,737 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:04,900 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (66/116): HZ_ZJ_527.xlsx
2025-05-27 10:10:04,910 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.6% (阈值: 80%)
2025-05-27 10:10:04,911 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:05,391 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:05,457 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (67/116): HZ_ZJ_531.xlsx
2025-05-27 10:10:05,473 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.6% (阈值: 80%)
2025-05-27 10:10:05,473 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:05,966 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:06,291 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (68/116): HZ_ZJ_534.xlsx
2025-05-27 10:10:06,303 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.7% (阈值: 80%)
2025-05-27 10:10:06,303 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:06,806 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:06,821 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (69/116): HZ_ZJ_545.xlsx
2025-05-27 10:10:06,841 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.8% (阈值: 80%)
2025-05-27 10:10:06,841 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:07,358 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:07,373 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (70/116): HZ_ZJ_547.xlsx
2025-05-27 10:10:07,387 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.8% (阈值: 80%)
2025-05-27 10:10:07,387 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:07,921 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:10:07,961 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (71/116): HZ_ZJ_551.xlsx
2025-05-27 10:10:07,972 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.8% (阈值: 80%)
2025-05-27 10:10:07,972 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:08,519 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:09,222 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (72/116): HZ_ZJ_556.xlsx
2025-05-27 10:10:09,222 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.9% (阈值: 80%)
2025-05-27 10:10:09,222 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:09,763 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:09,838 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (73/116): HZ_ZJ_584.xlsx
2025-05-27 10:10:09,839 - WARNING - check_memory_usage:129 - 内存使用率过高: 85.0% (阈值: 80%)
2025-05-27 10:10:09,839 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:10,385 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:10,589 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (74/116): HZ_ZJ_591.xlsx
2025-05-27 10:10:10,608 - WARNING - check_memory_usage:129 - 内存使用率过高: 85.1% (阈值: 80%)
2025-05-27 10:10:10,609 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:11,174 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:11,191 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (75/116): HZ_ZJ_592.xlsx
2025-05-27 10:10:11,194 - WARNING - check_memory_usage:129 - 内存使用率过高: 85.1% (阈值: 80%)
2025-05-27 10:10:11,194 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:11,763 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:12,521 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (76/116): HZ_ZJ_594.xlsx
2025-05-27 10:10:12,523 - WARNING - check_memory_usage:129 - 内存使用率过高: 85.3% (阈值: 80%)
2025-05-27 10:10:12,523 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:13,088 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:13,173 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (77/116): HZ_ZJ_595.xlsx
2025-05-27 10:10:13,189 - WARNING - check_memory_usage:129 - 内存使用率过高: 85.4% (阈值: 80%)
2025-05-27 10:10:13,190 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:13,756 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:13,823 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (78/116): HZ_ZJ_606.xlsx
2025-05-27 10:10:13,842 - WARNING - check_memory_usage:129 - 内存使用率过高: 85.3% (阈值: 80%)
2025-05-27 10:10:13,843 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:14,439 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:14,466 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (79/116): HZ_ZJ_608.xlsx
2025-05-27 10:10:14,474 - WARNING - check_memory_usage:129 - 内存使用率过高: 85.3% (阈值: 80%)
2025-05-27 10:10:14,474 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:15,040 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:15,056 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (80/116): HZ_ZJ_612.xlsx
2025-05-27 10:10:15,062 - WARNING - check_memory_usage:129 - 内存使用率过高: 85.3% (阈值: 80%)
2025-05-27 10:10:15,062 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:15,638 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:10:18,394 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (81/116): HZ_ZJ_627.xlsx
2025-05-27 10:10:18,404 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.2% (阈值: 80%)
2025-05-27 10:10:18,404 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:19,039 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:19,039 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (82/116): HZ_ZJ_628.xlsx
2025-05-27 10:10:19,039 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.2% (阈值: 80%)
2025-05-27 10:10:19,039 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:19,674 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:19,691 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (83/116): HZ_ZJ_637.xlsx
2025-05-27 10:10:19,699 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.2% (阈值: 80%)
2025-05-27 10:10:19,699 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:20,340 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:10:20,527 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (84/116): HZ_ZJ_650.xlsx
2025-05-27 10:10:20,536 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.2% (阈值: 80%)
2025-05-27 10:10:20,538 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:21,195 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:21,290 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (85/116): HZ_ZJ_659.xlsx
2025-05-27 10:10:21,304 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.2% (阈值: 80%)
2025-05-27 10:10:21,304 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:21,956 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:23,227 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (86/116): HZ_ZJ_663.xlsx
2025-05-27 10:10:23,237 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.6% (阈值: 80%)
2025-05-27 10:10:23,238 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:23,900 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:10:23,920 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (87/116): HZ_ZJ_664.xlsx
2025-05-27 10:10:23,932 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.6% (阈值: 80%)
2025-05-27 10:10:23,932 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:24,600 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:24,606 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (88/116): HZ_ZJ_672.xlsx
2025-05-27 10:10:24,625 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.6% (阈值: 80%)
2025-05-27 10:10:24,625 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:25,290 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:25,305 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (89/116): HZ_ZJ_676.xlsx
2025-05-27 10:10:25,315 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.6% (阈值: 80%)
2025-05-27 10:10:25,315 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:25,990 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:10:26,017 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (90/116): HZ_ZJ_687.xlsx
2025-05-27 10:10:26,024 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.7% (阈值: 80%)
2025-05-27 10:10:26,024 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:26,703 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:26,706 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (91/116): HZ_ZJ_690.xlsx
2025-05-27 10:10:26,717 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.6% (阈值: 80%)
2025-05-27 10:10:26,717 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:27,389 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:27,389 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (92/116): HZ_ZJ_693.xlsx
2025-05-27 10:10:27,409 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.6% (阈值: 80%)
2025-05-27 10:10:27,410 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:28,072 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:28,112 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.7% (阈值: 80%)
2025-05-27 10:10:28,788 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 0
2025-05-27 10:10:29,559 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (93/116): HZ_ZJ_700.xlsx
2025-05-27 10:10:29,559 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.8% (阈值: 80%)
2025-05-27 10:10:29,571 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:30,269 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:30,473 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (94/116): HZ_ZJ_702.xlsx
2025-05-27 10:10:30,495 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.9% (阈值: 80%)
2025-05-27 10:10:30,495 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:31,157 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:31,639 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (95/116): HZ_ZJ_723.xlsx
2025-05-27 10:10:31,650 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.1% (阈值: 80%)
2025-05-27 10:10:31,650 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:32,354 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:10:32,842 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (96/116): HZ_ZJ_724.xlsx
2025-05-27 10:10:32,854 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.2% (阈值: 80%)
2025-05-27 10:10:32,854 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:33,571 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:33,682 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (97/116): HZ_ZJ_726.xlsx
2025-05-27 10:10:33,694 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.1% (阈值: 80%)
2025-05-27 10:10:33,694 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:34,381 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:34,582 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (98/116): HZ_ZJ_732.xlsx
2025-05-27 10:10:34,592 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.2% (阈值: 80%)
2025-05-27 10:10:34,593 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:35,297 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:35,311 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (99/116): HZ_ZJ_733.xlsx
2025-05-27 10:10:35,324 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.2% (阈值: 80%)
2025-05-27 10:10:35,324 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:36,055 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:36,074 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (100/116): HZ_ZJ_734.xlsx
2025-05-27 10:10:36,077 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.2% (阈值: 80%)
2025-05-27 10:10:36,077 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:36,772 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:36,800 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (101/116): HZ_ZJ_736.xlsx
2025-05-27 10:10:36,811 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.2% (阈值: 80%)
2025-05-27 10:10:36,811 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:37,555 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:41,093 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (102/116): HZ_ZJ_737.xlsx
2025-05-27 10:10:41,115 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.8% (阈值: 80%)
2025-05-27 10:10:41,115 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:41,910 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:41,922 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (103/116): HZ_ZJ_743.xlsx
2025-05-27 10:10:41,927 - WARNING - check_memory_usage:129 - 内存使用率过高: 84.8% (阈值: 80%)
2025-05-27 10:10:41,927 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:42,716 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:51,134 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (104/116): HZ_ZJ_747.xlsx
2025-05-27 10:10:51,138 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.1% (阈值: 80%)
2025-05-27 10:10:51,138 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:52,122 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 10
2025-05-27 10:10:52,143 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (105/116): HZ_ZJ_759.xlsx
2025-05-27 10:10:52,155 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.2% (阈值: 80%)
2025-05-27 10:10:52,155 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:53,105 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:53,143 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (106/116): HZ_ZJ_760.xlsx
2025-05-27 10:10:53,158 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.2% (阈值: 80%)
2025-05-27 10:10:53,158 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:54,140 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:54,140 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (107/116): HZ_ZJ_770.xlsx
2025-05-27 10:10:54,157 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.2% (阈值: 80%)
2025-05-27 10:10:54,158 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:55,224 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:10:56,775 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (108/116): HZ_ZJ_771.xlsx
2025-05-27 10:10:56,784 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.6% (阈值: 80%)
2025-05-27 10:10:56,784 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:57,755 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 122
2025-05-27 10:10:57,952 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (109/116): HZ_ZJ_772.xlsx
2025-05-27 10:10:57,967 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.7% (阈值: 80%)
2025-05-27 10:10:57,967 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:10:58,950 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.7% (阈值: 80%)
2025-05-27 10:10:58,951 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 0
2025-05-27 10:10:58,951 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:10:59,047 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (110/116): HZ_ZJ_774.xlsx
2025-05-27 10:10:59,058 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.8% (阈值: 80%)
2025-05-27 10:10:59,058 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:11:00,072 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:11:00,093 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (111/116): HZ_ZJ_775.xlsx
2025-05-27 10:11:00,106 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.9% (阈值: 80%)
2025-05-27 10:11:00,106 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:11:01,117 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:11:01,122 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (112/116): HZ_ZJ_777.xlsx
2025-05-27 10:11:01,139 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.9% (阈值: 80%)
2025-05-27 10:11:01,139 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:11:02,106 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 132
2025-05-27 10:11:02,174 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (113/116): HZ_ZJ_781.xlsx
2025-05-27 10:11:02,188 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.9% (阈值: 80%)
2025-05-27 10:11:02,189 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:11:03,206 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:11:03,372 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (114/116): HZ_ZJ_782.xlsx
2025-05-27 10:11:03,382 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.9% (阈值: 80%)
2025-05-27 10:11:03,382 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:11:04,373 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:11:04,442 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (115/116): HZ_ZJ_787.xlsx
2025-05-27 10:11:04,442 - WARNING - check_memory_usage:129 - 内存使用率过高: 87.9% (阈值: 80%)
2025-05-27 10:11:04,442 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:11:05,439 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:11:05,460 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (116/116): HZ_ZJ_788.xlsx
2025-05-27 10:11:05,460 - WARNING - check_memory_usage:129 - 内存使用率过高: 88.0% (阈值: 80%)
2025-05-27 10:11:05,460 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:11:06,458 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:11:06,536 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:11:06,537 - ERROR - safe_file_context:458 - 文件操作失败: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:11:06,537 - ERROR - _merge_xlsx_files_impl:982 - 合并文件时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:11:06,537 - ERROR - _merge_xlsx_files_impl:983 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file, config)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:11:11,555 - INFO - stop_monitoring:203 - 资源监控已停止
2025-05-27 10:11:11,556 - ERROR - merge_xlsx_files:756 - 文件合并发生不可重试的异常: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:11:11,556 - ERROR - _process_single_folder:1020 - 处理文件夹 C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33112500431-云和县人民医院（云和县医疗健康集团人民医院院区） 时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:11:11,557 - ERROR - _process_single_folder:1021 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1018, in _process_single_folder
    return merge_xlsx_files(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 740, in merge_xlsx_files
    return _merge_xlsx_files_impl(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file, config)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:11:11,557 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:11:11,557 - INFO - load_checkpoint:222 - 成功加载检查点文件: checkpoint.json
2025-05-27 10:11:11,567 - WARNING - check_memory_usage:129 - 内存使用率过高: 86.8% (阈值: 80%)
2025-05-27 10:11:11,567 - INFO - start_monitoring:196 - 资源监控已启动
2025-05-27 10:11:13,782 - INFO - _merge_xlsx_files_impl:768 - 开始处理文件夹: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33112500432-云和县中医医院（云和县医疗健康集团中医医院院区）
2025-05-27 10:11:13,782 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 10200298
2025-05-27 10:11:13,785 - INFO - _merge_xlsx_files_impl:805 - 找到 35 个Excel文件
2025-05-27 10:11:13,787 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (1/35): HZ_ZJ_023.xlsx
2025-05-27 10:11:13,804 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (2/35): HZ_ZJ_028.xlsx
2025-05-27 10:11:13,829 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (3/35): HZ_ZJ_165.xlsx
2025-05-27 10:11:13,841 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (4/35): HZ_ZJ_193.xlsx
2025-05-27 10:11:13,891 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (5/35): HZ_ZJ_292.xlsx
2025-05-27 10:11:13,914 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (6/35): HZ_ZJ_299.xlsx
2025-05-27 10:11:13,943 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (7/35): HZ_ZJ_326.xlsx
2025-05-27 10:11:14,088 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (8/35): HZ_ZJ_347.xlsx
2025-05-27 10:11:14,423 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (9/35): HZ_ZJ_426.xlsx
2025-05-27 10:11:14,639 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (10/35): HZ_ZJ_437.xlsx
2025-05-27 10:11:14,924 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (11/35): HZ_ZJ_438.xlsx
2025-05-27 10:11:15,749 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (12/35): HZ_ZJ_440.xlsx
2025-05-27 10:11:16,050 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (13/35): HZ_ZJ_441.xlsx
2025-05-27 10:11:16,071 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (14/35): HZ_ZJ_442.xlsx
2025-05-27 10:11:16,415 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (15/35): HZ_ZJ_444.xlsx
2025-05-27 10:11:16,456 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (16/35): HZ_ZJ_450.xlsx
2025-05-27 10:11:16,496 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (17/35): HZ_ZJ_465.xlsx
2025-05-27 10:11:16,524 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (18/35): HZ_ZJ_466.xlsx
2025-05-27 10:11:16,656 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (19/35): HZ_ZJ_518.xlsx
2025-05-27 10:11:16,722 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (20/35): HZ_ZJ_527.xlsx
2025-05-27 10:11:16,756 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (21/35): HZ_ZJ_547.xlsx
2025-05-27 10:11:18,607 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (22/35): HZ_ZJ_551.xlsx
2025-05-27 10:11:18,883 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (23/35): HZ_ZJ_650.xlsx
2025-05-27 10:11:18,910 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (24/35): HZ_ZJ_659.xlsx
2025-05-27 10:11:19,116 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (25/35): HZ_ZJ_700.xlsx
2025-05-27 10:11:19,405 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (26/35): HZ_ZJ_711.xlsx
2025-05-27 10:11:19,434 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (27/35): HZ_ZJ_726.xlsx
2025-05-27 10:11:19,462 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (28/35): HZ_ZJ_736.xlsx
2025-05-27 10:11:21,036 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (29/35): HZ_ZJ_743.xlsx
2025-05-27 10:11:30,488 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (30/35): HZ_ZJ_753.xlsx
2025-05-27 10:11:30,527 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (31/35): HZ_ZJ_770.xlsx
2025-05-27 10:11:30,574 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (32/35): HZ_ZJ_774.xlsx
2025-05-27 10:11:30,658 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (33/35): HZ_ZJ_780.xlsx
2025-05-27 10:11:31,179 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (34/35): HZ_ZJ_788.xlsx
2025-05-27 10:11:31,224 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (35/35): 中医定向透药疗法.xlsx
2025-05-27 10:11:31,390 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:11:31,390 - ERROR - safe_file_context:458 - 文件操作失败: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:11:31,390 - ERROR - _merge_xlsx_files_impl:982 - 合并文件时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:11:31,390 - ERROR - _merge_xlsx_files_impl:983 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file, config)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:11:36,398 - INFO - stop_monitoring:203 - 资源监控已停止
2025-05-27 10:11:36,398 - ERROR - merge_xlsx_files:756 - 文件合并发生不可重试的异常: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:11:36,398 - ERROR - _process_single_folder:1020 - 处理文件夹 C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33112500432-云和县中医医院（云和县医疗健康集团中医医院院区） 时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:11:36,400 - ERROR - _process_single_folder:1021 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1018, in _process_single_folder
    return merge_xlsx_files(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 740, in merge_xlsx_files
    return _merge_xlsx_files_impl(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:11:36,402 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:11:36,402 - INFO - load_checkpoint:222 - 成功加载检查点文件: checkpoint.json
2025-05-27 10:11:36,409 - INFO - start_monitoring:196 - 资源监控已启动
2025-05-27 10:11:36,409 - INFO - _merge_xlsx_files_impl:768 - 开始处理文件夹: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33118100027-龙泉市中医医院（龙泉市中医院医共体）
2025-05-27 10:11:36,416 - INFO - _merge_xlsx_files_impl:805 - 找到 51 个Excel文件
2025-05-27 10:11:36,417 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (1/51): HZ_ZJ_025.xlsx
2025-05-27 10:11:37,190 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (2/51): HZ_ZJ_028.xlsx
2025-05-27 10:11:37,238 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (3/51): HZ_ZJ_039.xlsx
2025-05-27 10:11:37,255 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (4/51): HZ_ZJ_046.xlsx
2025-05-27 10:11:37,374 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (5/51): HZ_ZJ_079.xlsx
2025-05-27 10:11:37,396 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (6/51): HZ_ZJ_085.xlsx
2025-05-27 10:11:37,416 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (7/51): HZ_ZJ_165.xlsx
2025-05-27 10:11:37,424 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (8/51): HZ_ZJ_235.xlsx
2025-05-27 10:11:37,462 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (9/51): HZ_ZJ_289.xlsx
2025-05-27 10:11:37,509 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (10/51): HZ_ZJ_292.xlsx
2025-05-27 10:11:37,541 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (11/51): HZ_ZJ_302.xlsx
2025-05-27 10:11:37,690 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (12/51): HZ_ZJ_306.xlsx
2025-05-27 10:11:37,736 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (13/51): HZ_ZJ_324.xlsx
2025-05-27 10:11:37,741 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (14/51): HZ_ZJ_326.xlsx
2025-05-27 10:11:46,318 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (15/51): HZ_ZJ_394.xlsx
2025-05-27 10:11:46,342 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (16/51): HZ_ZJ_426.xlsx
2025-05-27 10:11:46,359 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (17/51): HZ_ZJ_432.xlsx
2025-05-27 10:11:46,392 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (18/51): HZ_ZJ_437.xlsx
2025-05-27 10:11:46,831 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (19/51): HZ_ZJ_438.xlsx
2025-05-27 10:11:47,142 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (20/51): HZ_ZJ_440.xlsx
2025-05-27 10:11:47,273 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (21/51): HZ_ZJ_442.xlsx
2025-05-27 10:11:47,505 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (22/51): HZ_ZJ_443.xlsx
2025-05-27 10:11:49,128 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (23/51): HZ_ZJ_444.xlsx
2025-05-27 10:11:49,225 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (24/51): HZ_ZJ_447.xlsx
2025-05-27 10:11:49,634 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (25/51): HZ_ZJ_449.xlsx
2025-05-27 10:11:49,788 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (26/51): HZ_ZJ_458.xlsx
2025-05-27 10:11:49,807 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (27/51): HZ_ZJ_465.xlsx
2025-05-27 10:11:49,848 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (28/51): HZ_ZJ_510.xlsx
2025-05-27 10:11:49,872 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (29/51): HZ_ZJ_518.xlsx
2025-05-27 10:11:49,896 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (30/51): HZ_ZJ_527.xlsx
2025-05-27 10:11:49,958 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (31/51): HZ_ZJ_530.xlsx
2025-05-27 10:11:49,976 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (32/51): HZ_ZJ_547.xlsx
2025-05-27 10:11:50,039 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (33/51): HZ_ZJ_566.xlsx
2025-05-27 10:11:50,056 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (34/51): HZ_ZJ_584.xlsx
2025-05-27 10:11:50,131 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (35/51): HZ_ZJ_606.xlsx
2025-05-27 10:11:50,168 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (36/51): HZ_ZJ_627.xlsx
2025-05-27 10:11:50,183 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (37/51): HZ_ZJ_637.xlsx
2025-05-27 10:11:50,200 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (38/51): HZ_ZJ_659.xlsx
2025-05-27 10:11:50,375 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (39/51): HZ_ZJ_683.xlsx
2025-05-27 10:11:50,392 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (40/51): HZ_ZJ_721.xlsx
2025-05-27 10:11:50,418 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (41/51): HZ_ZJ_726.xlsx
2025-05-27 10:11:50,466 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (42/51): HZ_ZJ_743.xlsx
2025-05-27 10:11:57,056 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (43/51): HZ_ZJ_759.xlsx
2025-05-27 10:11:57,078 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (44/51): HZ_ZJ_760.xlsx
2025-05-27 10:11:57,241 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (45/51): HZ_ZJ_774.xlsx
2025-05-27 10:11:57,656 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (46/51): HZ_ZJ_779.xlsx
2025-05-27 10:11:58,205 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (47/51): HZ_ZJ_780.xlsx
2025-05-27 10:12:06,672 - WARNING - check_memory_usage:129 - 内存使用率过高: 80.7% (阈值: 80%)
2025-05-27 10:12:07,300 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 0
2025-05-27 10:12:13,773 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (48/51): HZ_ZJ_787.xlsx
2025-05-27 10:12:13,797 - WARNING - check_memory_usage:129 - 内存使用率过高: 82.6% (阈值: 80%)
2025-05-27 10:12:13,797 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:12:14,511 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 10
2025-05-27 10:12:14,623 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (49/51): HZ_ZJ_788.xlsx
2025-05-27 10:12:14,636 - WARNING - check_memory_usage:129 - 内存使用率过高: 82.6% (阈值: 80%)
2025-05-27 10:12:14,638 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:12:15,339 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:12:15,405 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (50/51): HZ_ZJ_803.xlsx
2025-05-27 10:12:15,417 - WARNING - check_memory_usage:129 - 内存使用率过高: 82.6% (阈值: 80%)
2025-05-27 10:12:15,418 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:12:16,121 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:12:16,137 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (51/51): 中医定向透药疗法.xlsx
2025-05-27 10:12:16,148 - WARNING - check_memory_usage:129 - 内存使用率过高: 82.6% (阈值: 80%)
2025-05-27 10:12:16,148 - WARNING - _merge_xlsx_files_impl:828 - 内存使用率过高，执行垃圾回收
2025-05-27 10:12:16,857 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 112
2025-05-27 10:12:17,406 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:12:17,406 - ERROR - safe_file_context:458 - 文件操作失败: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:17,406 - ERROR - _merge_xlsx_files_impl:982 - 合并文件时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:17,406 - ERROR - _merge_xlsx_files_impl:983 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:12:22,424 - INFO - stop_monitoring:203 - 资源监控已停止
2025-05-27 10:12:22,424 - ERROR - merge_xlsx_files:756 - 文件合并发生不可重试的异常: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:22,425 - ERROR - _process_single_folder:1020 - 处理文件夹 C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33118100027-龙泉市中医医院（龙泉市中医院医共体） 时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:22,425 - ERROR - _process_single_folder:1021 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1018, in _process_single_folder
    return merge_xlsx_files(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 740, in merge_xlsx_files
    return _merge_xlsx_files_impl(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:12:22,427 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:12:22,427 - INFO - load_checkpoint:222 - 成功加载检查点文件: checkpoint.json
2025-05-27 10:12:22,447 - INFO - start_monitoring:196 - 资源监控已启动
2025-05-27 10:12:22,449 - WARNING - check_memory_usage:129 - 内存使用率过高: 83.5% (阈值: 80%)
2025-05-27 10:12:22,449 - INFO - _merge_xlsx_files_impl:768 - 开始处理文件夹: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33118100034-龙泉市中医院医共体锦溪双岭服务站
2025-05-27 10:12:23,929 - INFO - force_garbage_collection:155 - 垃圾回收完成，回收对象数: 6914092
2025-05-27 10:12:23,932 - INFO - _merge_xlsx_files_impl:805 - 找到 1 个Excel文件
2025-05-27 10:12:23,933 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (1/1): HZ_ZJ_477.xlsx
2025-05-27 10:12:23,969 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:12:23,970 - ERROR - safe_file_context:458 - 文件操作失败: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:23,970 - ERROR - _merge_xlsx_files_impl:982 - 合并文件时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:23,970 - ERROR - _merge_xlsx_files_impl:983 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:12:28,979 - INFO - stop_monitoring:203 - 资源监控已停止
2025-05-27 10:12:28,979 - ERROR - merge_xlsx_files:756 - 文件合并发生不可重试的异常: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:28,979 - ERROR - _process_single_folder:1020 - 处理文件夹 C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33118100034-龙泉市中医院医共体锦溪双岭服务站 时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:28,979 - ERROR - _process_single_folder:1021 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1018, in _process_single_folder
    return merge_xlsx_files(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 740, in merge_xlsx_files
    return _merge_xlsx_files_impl(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:12:28,979 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:12:28,979 - INFO - load_checkpoint:222 - 成功加载检查点文件: checkpoint.json
2025-05-27 10:12:28,991 - INFO - start_monitoring:196 - 资源监控已启动
2025-05-27 10:12:28,991 - INFO - _merge_xlsx_files_impl:768 - 开始处理文件夹: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33119900780-丽水田氏骨科医院有限公司
2025-05-27 10:12:28,999 - INFO - _merge_xlsx_files_impl:805 - 找到 18 个Excel文件
2025-05-27 10:12:29,000 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (1/18): HZ_ZJ_013.xlsx
2025-05-27 10:12:29,017 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (2/18): HZ_ZJ_039.xlsx
2025-05-27 10:12:29,035 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (3/18): HZ_ZJ_085.xlsx
2025-05-27 10:12:29,056 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (4/18): HZ_ZJ_292.xlsx
2025-05-27 10:12:29,074 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (5/18): HZ_ZJ_324.xlsx
2025-05-27 10:12:29,091 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (6/18): HZ_ZJ_338.xlsx
2025-05-27 10:12:29,129 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (7/18): HZ_ZJ_401.xlsx
2025-05-27 10:12:29,257 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (8/18): HZ_ZJ_423.xlsx
2025-05-27 10:12:29,275 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (9/18): HZ_ZJ_438.xlsx
2025-05-27 10:12:29,320 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (10/18): HZ_ZJ_443.xlsx
2025-05-27 10:12:29,351 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (11/18): HZ_ZJ_455.xlsx
2025-05-27 10:12:29,400 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (12/18): HZ_ZJ_545.xlsx
2025-05-27 10:12:29,412 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (13/18): HZ_ZJ_547.xlsx
2025-05-27 10:12:29,446 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (14/18): HZ_ZJ_627.xlsx
2025-05-27 10:12:29,457 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (15/18): HZ_ZJ_692.xlsx
2025-05-27 10:12:29,477 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (16/18): HZ_ZJ_743.xlsx
2025-05-27 10:12:29,540 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (17/18): HZ_ZJ_770.xlsx
2025-05-27 10:12:29,590 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (18/18): HZ_ZJ_772.xlsx
2025-05-27 10:12:29,624 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:12:29,624 - ERROR - safe_file_context:458 - 文件操作失败: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:29,624 - ERROR - _merge_xlsx_files_impl:982 - 合并文件时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:29,624 - ERROR - _merge_xlsx_files_impl:983 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:12:34,637 - INFO - stop_monitoring:203 - 资源监控已停止
2025-05-27 10:12:34,637 - ERROR - merge_xlsx_files:756 - 文件合并发生不可重试的异常: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:34,637 - ERROR - _process_single_folder:1020 - 处理文件夹 C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33119900780-丽水田氏骨科医院有限公司 时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:34,639 - ERROR - _process_single_folder:1021 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1018, in _process_single_folder
    return merge_xlsx_files(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 740, in merge_xlsx_files
    return _merge_xlsx_files_impl(source_folder, target_file, data, config)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

2025-05-27 10:12:34,640 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:12:34,640 - INFO - load_checkpoint:222 - 成功加载检查点文件: checkpoint.json
2025-05-27 10:12:34,651 - INFO - start_monitoring:196 - 资源监控已启动
2025-05-27 10:12:34,651 - INFO - _merge_xlsx_files_impl:768 - 开始处理文件夹: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分\H33119900912-丽水口腔医院
2025-05-27 10:12:34,654 - INFO - _merge_xlsx_files_impl:805 - 找到 3 个Excel文件
2025-05-27 10:12:34,655 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (1/3): HZ_ZJ_606.xlsx
2025-05-27 10:12:34,696 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (2/3): HZ_ZJ_684.xlsx
2025-05-27 10:12:34,763 - INFO - _merge_xlsx_files_impl:823 - 正在处理文件 (3/3): HZ_ZJ_803.xlsx
2025-05-27 10:12:35,007 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:12:35,007 - ERROR - safe_file_context:458 - 文件操作失败: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:35,008 - ERROR - _merge_xlsx_files_impl:982 - 合并文件时发生错误: Workbook.save() takes 2 positional arguments but 3 were given
2025-05-27 10:12:35,008 - ERROR - _merge_xlsx_files_impl:983 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 964, in _merge_xlsx_files_impl
    safe_file_operation(wb.save, target_file)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 517, in safe_file_operation
    result = operation(*new_args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Workbook.save() takes 2 positional arguments but 3 were given

