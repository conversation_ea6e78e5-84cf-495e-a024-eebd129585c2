# Nuitka 构建说明 - hzhz.py

## 概述

本文档说明如何使用Nuitka将hzhz.py打包成独立的可执行文件(.exe)。

## 前置要求

### 系统要求
- Windows 10/11 (64位)
- Python 3.7+ (推荐Python 3.9+)
- 至少4GB可用内存
- 至少2GB可用磁盘空间

### 必需软件
1. **Python**: 从 https://python.org 下载安装
2. **C++编译器**: 
   - 推荐安装 Visual Studio Build Tools
   - 或者使用MinGW64 (Nuitka会自动下载)

## 构建方法

### 方法1: 使用Python脚本 (推荐)

```bash
python nuitka_build.py
```

这是最简单的方法，脚本会自动：
- 检查环境
- 安装Nuitka和依赖
- 清理旧文件
- 构建可执行文件

### 方法2: 使用批处理脚本

```bash
build_simple.bat
```

或使用完整版本：

```bash
build.bat
```

### 方法3: 手动构建

1. **安装Nuitka和依赖**:
```bash
pip install nuitka openpyxl psutil
```

2. **基本构建命令**:
```bash
nuitka --onefile --output-filename=hzhz.exe --output-dir=dist hzhz.py
```

3. **完整构建命令** (推荐):
```bash
nuitka ^
    --onefile ^
    --output-filename=hzhz.exe ^
    --output-dir=dist ^
    --assume-yes-for-downloads ^
    --plugin-enable=multiprocessing ^
    --windows-console-mode=force ^
    --mingw64 ^
    hzhz.py
```

### 方法4: 使用配置文件

```bash
nuitka --config-file=nuitka.config hzhz.py
```

## 构建选项说明

| 选项 | 说明 |
|------|------|
| `--onefile` | 生成单个可执行文件 |
| `--standalone` | 独立模式，包含所有依赖 |
| `--output-filename` | 指定输出文件名 |
| `--output-dir` | 指定输出目录 |
| `--plugin-enable=multiprocessing` | 启用多进程支持 |
| `--windows-console-mode=force` | 强制控制台模式 |
| `--mingw64` | 使用MinGW64编译器 |
| `--assume-yes-for-downloads` | 自动下载依赖 |

## 高级选项

### 优化选项
```bash
--lto=no                    # 禁用链接时优化(加快构建)
--jobs=4                    # 并行编译作业数
--remove-output             # 构建前清理输出目录
```

### 包含/排除选项
```bash
--include-package=openpyxl  # 强制包含包
--nofollow-import-to=tkinter # 排除不需要的包
```

### 元数据选项
```bash
--company-name="公司名称"
--product-name="产品名称"
--file-version=*******
--file-description="程序描述"
```

## 构建过程

### 典型构建流程
1. **环境检查** (30秒)
2. **依赖下载** (1-3分钟)
3. **代码分析** (1-2分钟)
4. **编译链接** (3-8分钟)
5. **打包优化** (1-2分钟)

**总时间**: 通常5-15分钟，取决于机器性能

### 构建输出
```
dist/
├── hzhz.exe          # 主可执行文件 (通常20-50MB)
└── (临时文件会被自动清理)
```

## 常见问题

### 1. 构建失败
**问题**: `error: Microsoft Visual C++ 14.0 is required`
**解决**: 安装 Visual Studio Build Tools 或使用 `--mingw64` 选项

### 2. 内存不足
**问题**: 构建过程中内存不足
**解决**: 
- 关闭其他程序
- 使用 `--jobs=1` 减少并行度
- 增加虚拟内存

### 3. 文件过大
**问题**: 生成的exe文件太大
**解决**:
- 使用 `--nofollow-import-to` 排除不需要的包
- 考虑使用 `--standalone` 而不是 `--onefile`

### 4. 运行时错误
**问题**: exe运行时出现模块导入错误
**解决**:
- 使用 `--include-package` 强制包含缺失的包
- 检查是否有隐式导入

### 5. 多进程问题
**问题**: 多进程功能不工作
**解决**: 确保使用了 `--plugin-enable=multiprocessing`

## 部署说明

### 文件清单
部署时需要的文件：
- `hzhz.exe` (主程序)
- `工作簿12.xlsx` (数据字典，必需)
- `disaster_config.ini` (配置文件，可选)

### 目标机器要求
- Windows 7+ (64位)
- 无需安装Python
- 无需安装其他依赖
- 建议4GB+内存

### 首次运行
- 首次启动可能需要5-10秒
- 会自动创建日志目录和配置文件
- 检查系统资源并显示建议

## 性能对比

| 方式 | 启动时间 | 内存占用 | 文件大小 | 部署复杂度 |
|------|----------|----------|----------|------------|
| Python脚本 | 1-2秒 | 50-100MB | 几KB | 高 |
| Nuitka单文件 | 3-5秒 | 80-150MB | 20-50MB | 低 |
| Nuitka独立 | 1-2秒 | 60-120MB | 100-200MB | 中 |

## 故障排除

### 构建日志
Nuitka会生成详细的构建日志，位于：
- `hzhz.build/` 目录
- 包含编译错误和警告信息

### 调试模式
使用调试模式构建：
```bash
nuitka --debug --onefile hzhz.py
```

### 获取帮助
```bash
nuitka --help
nuitka --help-plugins
```

## 自动化构建

### CI/CD集成
可以将构建过程集成到CI/CD流水线中：

```yaml
# GitHub Actions示例
- name: Build with Nuitka
  run: |
    pip install nuitka openpyxl psutil
    nuitka --onefile --output-filename=hzhz.exe hzhz.py
```

### 批量构建
创建批量构建脚本处理多个版本或配置。

## 许可证注意事项

- Nuitka是开源软件，遵循Apache 2.0许可证
- 生成的可执行文件可以自由分发
- 注意第三方库的许可证要求

## 更新和维护

### 版本更新
- 更新源代码后重新构建
- 考虑版本号管理
- 测试新版本的兼容性

### 性能监控
- 监控可执行文件的性能
- 收集用户反馈
- 优化构建配置

---

**提示**: 建议在正式部署前在目标环境中充分测试可执行文件。
