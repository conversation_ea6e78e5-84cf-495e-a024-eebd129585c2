@echo off
chcp 65001 >nul
echo ========================================
echo    使用Nuitka构建hzhz.exe
echo ========================================
echo.

REM 检查Python
echo [1/6] 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ 错误: Python未安装或未添加到PATH
    echo 请先安装Python 3.7+
    pause
    exit /b 1
)
echo ✅ Python环境正常
echo.

REM 检查源文件
echo [2/6] 检查源文件...
if not exist "hzhz.py" (
    echo ❌ 错误: hzhz.py 文件不存在
    pause
    exit /b 1
)
echo ✅ 源文件存在
echo.

REM 安装Nuitka
echo [3/6] 安装Nuitka...
python -m pip install nuitka --upgrade
if errorlevel 1 (
    echo ❌ Nuitka安装失败
    pause
    exit /b 1
)
echo ✅ Nuitka安装完成
echo.

REM 安装依赖
echo [4/6] 安装运行时依赖...
python -m pip install openpyxl psutil
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成
echo.

REM 清理旧的构建文件
echo [5/6] 清理旧构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "hzhz.build" rmdir /s /q "hzhz.build"
if exist "hzhz.dist" rmdir /s /q "hzhz.dist"
echo ✅ 清理完成
echo.

REM 构建可执行文件
echo [6/6] 开始构建可执行文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

nuitka ^
    --standalone ^
    --onefile ^
    --output-filename=hzhz.exe ^
    --output-dir=dist ^
    --assume-yes-for-downloads ^
    --plugin-enable=multiprocessing ^
    --windows-console-mode=force ^
    --disable-console ^
    --windows-icon-from-ico=icon.ico ^
    --company-name="Excel合并工具" ^
    --product-name="Excel文件合并工具" ^
    --file-version=******* ^
    --product-version=2.0.0 ^
    --file-description="Excel文件合并工具" ^
    --copyright="Copyright 2024" ^
    hzhz.py

if errorlevel 1 (
    echo.
    echo ❌ 构建失败！
    echo 请检查错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ 构建成功完成！
echo ========================================
echo.

REM 检查输出文件
if exist "dist\hzhz.exe" (
    for %%I in ("dist\hzhz.exe") do set size=%%~zI
    set /a size_mb=!size!/1024/1024
    echo 📁 可执行文件位置: dist\hzhz.exe
    echo 📊 文件大小: %size% 字节
    echo.
    echo 🎉 打包完成！
    echo.
    echo 📋 使用说明:
    echo 1. 将 dist\hzhz.exe 复制到目标机器
    echo 2. 确保目标机器有 工作簿12.xlsx 数据字典文件
    echo 3. 直接双击运行 hzhz.exe
    echo.
    echo 💡 提示: 首次运行可能需要几秒钟启动时间
) else (
    echo ❌ 错误: 可执行文件未生成
)

echo.
pause
