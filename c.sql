
WITH TAB1 AS (
select
	定点机构编码,
	定点机构名称,
	结算id,
	身份证号,
	姓名,
	结算时间,
	lag(结算id, 1) over(partition by 身份证号  ORDER BY 结算时间 ) AS 上次结算id,
	lag(结算时间, 1) over(partition by 身份证号  ORDER BY 结算时间 ) AS 上次结算时间,
	lead(结算时间, 1) over(partition by 身份证号  ORDER BY 结算时间 ) AS 下次结算时间,
	lead(结算id, 1) over(partition by 身份证号  ORDER BY 结算时间 ) AS 下次结算id
FROM
	HZ_YB_FYJSXX WHERE 定点机构编码 like 'P3311%'  AND 符合政策范围内金额 <> 0),
TAB2 AS (
	SELECT  	结算id  FROM TAB1 WHERE    datediff(下次结算时间,结算时间,'DD') = 1 AND datediff(结算时间,上次结算时间,'DD') = 1
	union
	select 上次结算id  FROM TAB1 WHERE    datediff(下次结算时间,结算时间,'DD') = 1 AND datediff(结算时间,上次结算时间,'DD') = 1
	union
	select 下次结算id  FROM TAB1 WHERE    datediff(下次结算时间,结算时间,'DD') = 1 AND datediff(结算时间,上次结算时间,'DD') = 1
)
select * from HZ_YB_FYJSXX where 结算id  in (select 结算id from tab2)
order by 身份证号,结算日期





SELECT COUNT(1) FROM (
 select a.* FROM
 hz_yb_fyjsxx b
 join   hz_yb_fymxxx a
 on b.结算id = a.BILL_ID  and b.医疗费用总额 > 1000  and b.定点机构编码 like 'P3311%')
SELECT * FROM TEMP_szyxfwf LIMIT 10000

select *  from TEMP_szyxfwf ORDER BY bill_id,BILL_DETAIL_ID




select medical_code as 定点机构编码,medical_name as 定点机构名称,item_name as 医保项目名称,sum(num) as 数量,sum(money) as 金额 from hz_yb_fymxxx where medical_code like '_3311%' AND (item_NAME LIKE '数字影像服务费')
AND  TO_CHAR(clear_time,'YYYY') = '2024'
group by medical_code,medical_name,item_name



select * from hz_yb_fymxxx where medical_code = 'H33112300064' AND item_name = '龙血竭'

SELECT 	定点机构编码, 定点机构名称,COUNT(结算id) as 刷卡次数,SUM(医疗费用总额) AS 医疗费用总额,SUM(符合政策范围内金额) AS 医保范围金额  FROM HZ_YB_FYJSXX WHERE 定点机构编码 like 'P3311%'
and ((to_char(结算时间,'hh:mi:ss') BETWEEN '00:00:00' and '07:29:59') OR to_char(结算时间,'hh') >= '21')
group by 定点机构编码, 定点机构名称
order by COUNT(结算id) desc


select a.*,b.余额
from hz_yb_fymxxx a
LEFT join hz_yb_fyjsxx b
on a.bill_id = b.结算id
where A.medical_code in ('P33118100116','P33118100140','P33110200012','P33112100350','P33112200072')
AND ((to_char(A.clear_time,'hh:mi:ss')
BETWEEN '00:00:00' and '07:29:59') OR to_char(A.clear_time,'hh') >= '21')



~~~
--重复收费
create table TEMP_HZ_ZJ_359 LIFECYCLE 5 as select t.*,
       (case
         when t.item_code like '33070301700' then
          t.money
       end) money_rules
  from hz_yb_fymxxx_20222025 t
 where exists (select 1
          from hz_yb_fymxxx_20222025 a
         where a.pay_per_retio < 1
           and a.item_code = '33070301700' -- 胸腔闭式引流术
           and t.bill_id = a.bill_id
     and datetrunc(t.cost_time,'dd') = datetrunc(a.cost_time,'dd')
           and t.medical_code = a.medical_code)
   and exists (select 1
          from hz_yb_fymxxx_20222025 a
         where substr(a.item_code,1,4) in ('3307','3308','3310')  -- 编码3307/3308/3310
     and a.item_code != '33070301700'
           and t.bill_id = a.bill_id
           and datetrunc(t.cost_time,'dd') = datetrunc(a.cost_time,'dd')
           and t.medical_code = a.medical_code)
   and ((t.item_code like '33070301700' and
       t.pay_per_retio < 1) or substr(t.item_code,1,4) in ('3307','3308','3310'))
~~~

~~~
--重复收费
SELECT

t.*,

CASE WHEN t.医保目录名称 LIKE '泪小点扩张%' THEN t.金额 END AS money_rules

FROM jsmx t

WHERE t.单据号 IN (

SELECT a.单据号

FROM jsmx a

JOIN jsmx b ON

a.单据号 = b.单据号 AND

DATE_TRUNC('day', a.费用发生时间) = DATE_TRUNC('day', b.费用发生时间) AND

a.定点机构名称 = b.定点机构名称

WHERE

a.医保目录名称 LIKE '泪小点扩张%' AND

b.医保目录名称 LIKE '泪道冲洗%' AND

b.医保目录名称 NOT LIKE '泪小点扩张%'

GROUP BY a.单据号

)

AND (

t.医保目录名称 LIKE '泪小点扩张%' OR

t.医保目录名称 LIKE '泪道冲洗%'

)
~~~

~~~
--按人次
select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
wm_concat(distinct ',',t.unit_price) 单价 ,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额
from hz_yb_fymxxx t
where  t.item_code in ('31030003900')
group by
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,
t.item_name_hosp,
t.hospital_num,
to_char(t.cost_time,'yyyymmdd'),
t.in_time,
t.out_time


with  tab1 as  (select t.bill_id,
       to_char(t.cost_time,'yyyymmdd') as 结算时间,
       t.item_code 医保项目编码,
       t.item_name 医保项目名称,SUM(NUM) as 数量,SUM(money) as 金额 FROM hz_yb_fymxxx t where t.item_name = '淋巴结穿刺术'
group by t.bill_id,to_char(t.cost_time,'yyyymmdd') ,t.item_code ,t.item_name),
tab2 as (select t.bill_id,
       to_char(t.cost_time,'yyyymmdd') as 结算时间,
       wm_concat(',',t.item_code ) as 医保项目编码,
        wm_concat(',',t.item_name )  as 医保项目名称,SUM(NUM) as 数量,SUM(money) as 金额 FROM hz_yb_fymxxx t where t.item_name IN( '穿刺针','活检针')
group by t.bill_id,to_char(t.cost_time,'yyyymmdd')  ),
TAB3 as (
select tab1.bill_id,tab1.结算时间,TAB2.数量 from tab1 join  tab2 on tab1.bill_id = tab2.bill_id and tab1.结算时间 = tab2.结算时间
where tab1.数量 > tab2.数量)

 select
t.medical_code 医疗机构编码,
t.medical_name 医疗机构名称,
t.card_id 证件号码,
t.patient_name 患者姓名,
t.benefit_type 限制类型,
t.medical_mode 医疗类别,
t.bill_id 单据号,
t.hospital_id 门诊住院号,
t.item_code 医保项目编码,
t.item_name 医保项目名称,
t.item_code_hosp 机构收费编码,
t.item_name_hosp 机构收费名称,
to_char(t.cost_time,'yyyymmdd') 费用发生时间,
t.in_time 入院时间,
t.out_time 出院时间,
t.unit_price 单价 ,
t.hospital_num 住院天数,
sum(t.num) 数量,
sum(t.money) 金额, (case when t.item_name like '淋巴结穿刺术' then sum(t.num)  - TAB3.数量 end) as 超出数量, (case
         when t.item_name like '淋巴结穿刺术' then (sum(t.money) - TAB3.数量) * t.unit_price end) as 超出金额 from hz_yb_fymxxx t join TAB3 on t.bill_id = tab3.bill_id and to_char(t.cost_time,'yyyymmdd')  = tab3.结算时间
 where t.item_name IN( '穿刺针','活检针','淋巴结穿刺术')
GROUP BY
t.medical_code,
t.medical_name,
t.benefit_type,
t.medical_mode,
t.card_id,
t.patient_name,
t.bill_id,
t.hospital_id,
t.item_code,
t.item_name,
t.item_code_hosp,z
t.item_name_hosp,
t.hospital_num,
t.unit_price,
to_char(t.cost_time,'yyyymmdd'),
t.in_time,
t.out_time,
 TAB3.数量


 WITH TAB1 AS (
select
 定点机构编码,
 定点机构名称,
 结算id,
 身份证号,
 姓名,
 结算时间,
 lag(结算id, 1) over(partition by 身份证号  ORDER BY 结算时间 ) AS 上次结算id,
 lag(结算时间, 1) over(partition by 身份证号  ORDER BY 结算时间 ) AS 上次结算时间,
 lead(结算时间, 1) over(partition by 身份证号  ORDER BY 结算时间 ) AS 下次结算时间,
 lead(结算id, 1) over(partition by 身份证号  ORDER BY 结算时间 ) AS 下次结算id
FROM
 HZ_YB_FYJSXX WHERE 定点机构编码 like 'P3311%'  AND 符合政策范围内金额 <> 0 and 医疗费用总额 > 300 AND 医疗类别 not in ('门诊特病','门诊慢病')),
TAB2 AS (
 SELECT   结算id  FROM TAB1 WHERE    datediff(下次结算时间,结算时间,'DD') = 1 AND datediff(结算时间,上次结算时间,'DD') = 1
 union
 select 上次结算id  FROM TAB1 WHERE    datediff(下次结算时间,结算时间,'DD') = 1 AND datediff(结算时间,上次结算时间,'DD') = 1
 union
 select 下次结算id  FROM TAB1 WHERE    datediff(下次结算时间,结算时间,'DD') = 1 AND datediff(结算时间,上次结算时间,'DD') = 1
)
select * from hz_yb_fymxxx where bill_id  in (select 结算id from tab2)


desc quansheng_tjjczq_prd.dwd_fin_fee_list_d_ls

quansheng_tjjczq_prd.dwd_fin_fee_list_d_ls
quansheng_tjjczq_prd.dwd_prd_nat_data_dic_a
desc quansheng_tjjczq_prd.dwd_prd_nat_ETHDRUG_INFO
DRUG_GENNAME_D

show quansheng_tjjczq_prd tables;
SELECT table_name FROM information_schema.tables WHERE project_name='quansheng_tjjczq_prd';
SELECT DISTINCT TABLE_NAME
FROM META.TABLESd
WHERE DATABASE_NAME = '<your_database>';


select distinct 医疗机构编码,医疗机构名称 from jszd2 where 医疗机构编码 like '_3311%'

create table temp_hz_yb_scmgjd as
select distinct trim(a.insu_admdvs) AREA_CODE,
                trim(a.insu_admdvs) AREA_PERSON_CODE,
                a.FIXMEDINS_CODE MEDICAL_CODE,
                a.FIXMEDINS_NAME MEDICAL_NAME,
                a.psn_no SOCIAL_CARD,
                b.certno CARD_ID,
                b.PSN_NAME PATIENT_NAME,
                m.hosp_lv,
                DICT_FIND('INSUTYPE', b.insutype) BENEFIT_TYPE,
                DICT_FIND('MED_TYPE', b.MED_TYPE) MEDICAL_MODE,
                a.setl_id BILL_ID,
                a.BKKP_SN BILL_DETAIL_ID,
                m.ipt_otp_no HOSPITAL_ID,
                to_DATE(to_char(a.FEE_OCUR_TIME, 'yyyy-mm-dd hh24:mi:ss'),
                        'yyyy-mm-dd hh24:mi:ss') COST_TIME,
                to_DATE(to_char(b.setl_time, 'yyyy-mm-dd hh24:mi:ss'),
                        'yyyy-mm-dd hh24:mi:ss') clear_time,
                case
                  when length(trim(a.hilist_code)) = 15 and
                       trim(a.hilist_code) like '%00' then
                   substr(trim(a.hilist_code), 3, 11)
                  when length(trim(a.hilist_code)) = 27 and
                       (trim(a.hilist_code) like '%-%' or
                        trim(a.hilist_code) like '%f%') then
                   substr(trim(a.hilist_code), -11, 11)
                  when length(trim(a.hilist_code)) = 24 and
                       trim(a.hilist_code) like '%f%' then
                   substr(trim(a.hilist_code), -11, 11)
                  when length(trim(a.hilist_code)) = 13 and
                       trim(a.hilist_code) like '%00' then
                   substr(trim(a.hilist_code), 1, 11)
                  when trim(a.hilist_code) like 'f%' then
                   substr(trim(a.hilist_code), 2, 11)
                  when length(trim(a.hilist_code)) = 26 and
                       trim(a.hilist_code) like '%-%' then
                   substr(a.hilist_code,instr(a.hilist_code,'-')+1,100)
                  else
                   DECODE(a.hilist_code,
                          '',
                          trim(a.hilist_code),
                          replace(a.hilist_code, 'f', ''))
                end item_code,
                trim(a.HILIST_NAME) ITEM_NAME,
                a.MEDINS_LIST_CODG ITEM_CODE_HOSP,
                a.MEDINS_LIST_NAME ITEM_NAME_HOSP,
                DICT_FIND('MED_CHRGITM_TYPE', a.MED_CHRGITM_TYPE) CHARGE_TYPE,
                DICT_FIND('CHRGITM_LV', a.CHRGITM_LV) COST_TYPE,
                trim(a.pric) UNIT_PRICE,
                a.pric_uplmt_amt MAX_PRICE,
                '' DOSE,
                a.CNT NUM,
                a.det_item_fee_sumamt MONEY,
                a.SELFPAY_PROP PAY_PER_RETIO,
                a.inscp_amt MONEY_MEDICAL,
                a.PRESELFPAY_AMT MONEY_SELF_PAY,
                a.FULAMT_OWNPAY_AMT MONEY_SELF_OUT,
                a.dosform_name DOSAGE_FORM,
                a.SPEC SPEC,
                a.HI_NEGO_DRUG_FLAG HI_NEGO_DRUG_FLAG,
                a.PRODPLAC_TYPE BUS_PRODUCE,
                a.PRODNAME PRODNAME,
                a.PRCU_DRUG_FLAG,
                '' IS_RECIPEL,
                a.TCMDRUG_USED_WAY IS_SINGLE,
                a.RX_DRORD_NO RECIPEL_NO,
                '' WARD,
                nvl(m.ADM_DEPT_NAME, a.ACORD_DEPT_NAME) DEPT_NAME,
                a.BILG_DEPT_NAME DISCHARGE_DEPT_NAME,
                a.BILG_DR_CODE DOCTOR_CODE,
                a.BILG_DR_NAME DOCTOR_NAME,
                '' DOCTOR_CARD,
                nvl(m.ADM_BED, m.wardarea_bed) BED_NO,
                m.ipt_days HOSPITAL_NUM,
                to_DATE(b.begndate, 'yyyy-mm-dd') IN_TIME,
                m.ADM_DEPT_CODG IN_DIAGNOSE_CODE,
                m.MAIN_COND_DSCR IN_DIAGNOSE_NAME,
                to_DATE(b.enddate, 'yyyy-mm-dd') OUT_TIME,
                n.diag_code OUT_DIAGNOSE_CODE,
                n.diag_name OUT_DIAGNOSE_NAME,
                DICT_FIND('DSCG_WAY', m.DSCG_WAY) DISCHARGE_KIND
  from quansheng_tjjczq_prd.dwd_fin_fee_list_d_ls   a
  join quansheng_tjjczq_prd.dwd_fin_setl_d_ls b
    on a.setl_id = b.setl_id
   and b.refd_setl_flag = '0'
  and b.SETL_TYPE = '2'
  and b.INSUTYPE not in ('340', '350', '360')
  and b.fixmedins_code = 'H33112300244'
  and year(b.setl_time) >=2024
  left join quansheng_tjjczq_prd.dwd_dgn_mdtrt_d_ls m
    on a.mdtrt_id = m.mdtrt_id
   and a.fixmedins_code = m.fixmedins_code
  left join (select t.setl_id,t.mdtrt_id,WM_CONCAT(',',t.diag_code) diag_code,WM_CONCAT(',',t.diag_name) diag_name
from quansheng_tjjczq_prd.dwd_dgn_mdcs_fund_setl_list_diag_d_ls t
group by t.setl_id,t.mdtrt_id) n
    on a.setl_id = n.setl_id

select bill_id,BILL_DETAIL_ID from hz_yb_fymxxx  A where A.medical_code = 'H33112300244' and year(clear_time) >=2024
EXCEPT
select bill_id,BILL_DETAIL_ID from temp_hz_yb_scmgjd a where a.clear_time <= '2025-04-29 19:45:42'

select * from hz_yb_fymxxx where bill_id IN ('330000174556441421803527913900','330000174425187730703477072537')
select * from quansheng_tjjczq_prd.dwd_fin_setl_d_ls where setl_id in ('330000174556441421803527913900','330000174425187730703477072537')