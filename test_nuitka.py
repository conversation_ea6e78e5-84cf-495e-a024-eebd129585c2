#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Nuitka是否正确安装和配置
"""

import sys
import subprocess

def test_nuitka():
    """测试Nuitka安装"""
    print("测试Nuitka安装...")
    
    try:
        # 测试Nuitka模块导入
        result = subprocess.run([
            sys.executable, "-m", "nuitka", "--version"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(f"✅ Nuitka已正确安装: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Nuitka版本检查失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Nuitka命令超时")
        return False
    except Exception as e:
        print(f"❌ Nuitka测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n测试依赖包...")
    
    deps = ['openpyxl', 'psutil']
    all_ok = True
    
    for dep in deps:
        try:
            __import__(dep)
            print(f"✅ {dep} 已安装")
        except ImportError:
            print(f"❌ {dep} 未安装")
            all_ok = False
    
    return all_ok

def main():
    print("=" * 40)
    print("Nuitka 环境测试")
    print("=" * 40)
    
    nuitka_ok = test_nuitka()
    deps_ok = test_dependencies()
    
    print("\n" + "=" * 40)
    if nuitka_ok and deps_ok:
        print("✅ 环境检查通过，可以开始构建！")
        print("\n运行构建命令:")
        print("python nuitka_build.py")
        print("或")
        print("build_simple.bat")
    else:
        print("❌ 环境检查失败")
        if not nuitka_ok:
            print("请安装Nuitka: pip install nuitka")
        if not deps_ok:
            print("请安装依赖: pip install openpyxl psutil")
    
    print("=" * 40)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
