#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Nuitka构建脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, description=""):
    """运行命令并处理错误"""
    print(f"执行: {description or ' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def main():
    print("=" * 50)
    print("Nuitka 构建脚本 - hzhz.py")
    print("=" * 50)

    # 检查源文件
    if not Path("hzhz.py").exists():
        print("❌ 错误: hzhz.py 文件不存在")
        return False

    print("✅ 源文件检查通过")

    # 1. 安装Nuitka
    print("\n[1/4] 安装Nuitka...")
    if not run_command([sys.executable, "-m", "pip", "install", "nuitka"], "安装Nuitka"):
        print("❌ Nuitka安装失败")
        return False

    # 2. 安装依赖
    print("\n[2/4] 安装依赖包...")
    if not run_command([sys.executable, "-m", "pip", "install", "openpyxl", "psutil"], "安装依赖"):
        print("❌ 依赖安装失败")
        return False

    # 3. 清理旧文件
    print("\n[3/4] 清理旧构建文件...")
    for dir_name in ["dist", "hzhz.build", "hzhz.dist"]:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"已删除: {dir_name}")

    # 4. 构建可执行文件
    print("\n[4/4] 构建可执行文件...")
    print("这可能需要几分钟时间，请耐心等待...")

    # 基本构建命令 - 使用Python模块方式调用
    cmd = [
        sys.executable, "-m", "nuitka",
        "--onefile",                    # 单文件模式
        "--output-filename=hzhz.exe",   # 输出文件名
        "--output-dir=dist",            # 输出目录
        "--assume-yes-for-downloads",   # 自动下载
        "--plugin-enable=multiprocessing",  # 多进程支持
        "hzhz.py"                       # 源文件
    ]

    # Windows特定选项
    if os.name == 'nt':
        cmd.extend([
            "--windows-console-mode=force",  # 强制控制台模式
            "--mingw64",                     # 使用MinGW64编译器
        ])

    if not run_command(cmd, "构建可执行文件"):
        print("❌ 构建失败")
        return False

    # 检查输出
    exe_path = Path("dist/hzhz.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"\n✅ 构建成功！")
        print(f"📁 文件位置: {exe_path}")
        print(f"📊 文件大小: {size_mb:.1f} MB")

        print(f"\n📋 使用说明:")
        print(f"1. 将 {exe_path} 复制到目标机器")
        print(f"2. 确保有 工作簿12.xlsx 数据字典文件")
        print(f"3. 直接运行 hzhz.exe")

        return True
    else:
        print("❌ 可执行文件未生成")
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
