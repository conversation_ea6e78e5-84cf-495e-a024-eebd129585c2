#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
容灾机制测试脚本
用于验证新增的容灾功能是否正常工作
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from hzhz import (
    ConfigManager, 
    ResourceMonitor, 
    DisasterRecovery, 
    safe_file_operation,
    retry_on_failure,
    ProcessPoolManager
)

def test_config_manager():
    """测试配置管理器"""
    print("=" * 50)
    print("测试配置管理器")
    print("=" * 50)
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(suffix='.ini', delete=False) as f:
        config_file = f.name
    
    try:
        config = ConfigManager(config_file)
        
        # 测试获取配置
        max_retries = config.getint('DISASTER_RECOVERY', 'max_retries', 3)
        memory_threshold = config.getint('RESOURCE_MONITORING', 'memory_threshold', 80)
        auto_backup = config.getboolean('DISASTER_RECOVERY', 'auto_backup', True)
        
        print(f"✓ 最大重试次数: {max_retries}")
        print(f"✓ 内存阈值: {memory_threshold}%")
        print(f"✓ 自动备份: {auto_backup}")
        print("配置管理器测试通过")
        
    finally:
        # 清理临时文件
        if os.path.exists(config_file):
            os.unlink(config_file)

def test_resource_monitor():
    """测试资源监控器"""
    print("\n" + "=" * 50)
    print("测试资源监控器")
    print("=" * 50)
    
    config = ConfigManager()
    monitor = ResourceMonitor(config)
    
    # 测试内存检查
    memory_usage, memory_critical = monitor.check_memory_usage()
    print(f"✓ 内存使用率: {memory_usage:.1f}% (临界: {memory_critical})")
    
    # 测试磁盘检查
    disk_usage, disk_critical = monitor.check_disk_space('.')
    print(f"✓ 磁盘使用率: {disk_usage:.1f}% (临界: {disk_critical})")
    
    # 测试可用内存
    available_memory = monitor.get_available_memory_mb()
    print(f"✓ 可用内存: {available_memory:.1f}MB")
    
    print("资源监控器测试通过")

def test_disaster_recovery():
    """测试容灾恢复系统"""
    print("\n" + "=" * 50)
    print("测试容灾恢复系统")
    print("=" * 50)
    
    # 创建临时检查点文件
    with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
        checkpoint_file = f.name
    
    try:
        config = ConfigManager()
        recovery = DisasterRecovery(checkpoint_file, config)
        
        # 测试任务标记
        task_id = "test_task_001"
        
        # 标记进度
        recovery.mark_progress(task_id, {
            'status': 'processing',
            'current_step': 1,
            'total_steps': 10
        })
        print(f"✓ 标记任务进度: {task_id}")
        
        # 检查任务状态
        is_completed = recovery.is_completed(task_id)
        print(f"✓ 任务完成状态: {is_completed}")
        
        # 标记任务完成
        recovery.mark_completed(task_id, "/tmp/test_result.xlsx", {
            'files_processed': 5,
            'total_size': 1024000
        })
        print(f"✓ 标记任务完成: {task_id}")
        
        # 再次检查状态
        is_completed = recovery.is_completed(task_id)
        print(f"✓ 任务完成状态: {is_completed}")
        
        # 获取统计信息
        stats = recovery.get_statistics()
        print(f"✓ 统计信息: {stats}")
        
        print("容灾恢复系统测试通过")
        
    finally:
        # 清理临时文件
        if os.path.exists(checkpoint_file):
            os.unlink(checkpoint_file)
        # 清理备份文件
        for i in range(1, 4):
            backup_file = f"{checkpoint_file}.bak{i}"
            if os.path.exists(backup_file):
                os.unlink(backup_file)

@retry_on_failure(max_retries=2, delay=0.1)
def test_function_with_retry():
    """测试重试装饰器的函数"""
    # 这个函数会在第一次调用时失败，第二次成功
    if not hasattr(test_function_with_retry, 'call_count'):
        test_function_with_retry.call_count = 0
    
    test_function_with_retry.call_count += 1
    
    if test_function_with_retry.call_count == 1:
        raise ValueError("模拟第一次失败")
    
    return "成功"

def test_retry_mechanism():
    """测试重试机制"""
    print("\n" + "=" * 50)
    print("测试重试机制")
    print("=" * 50)
    
    try:
        result = test_function_with_retry()
        print(f"✓ 重试机制测试通过: {result}")
        print(f"✓ 总调用次数: {test_function_with_retry.call_count}")
    except Exception as e:
        print(f"✗ 重试机制测试失败: {e}")

def test_safe_file_operation():
    """测试安全文件操作"""
    print("\n" + "=" * 50)
    print("测试安全文件操作")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = os.path.join(temp_dir, "test.txt")
        
        # 定义一个简单的文件写入函数
        def write_file(filename, content="测试内容"):
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
        
        try:
            # 使用安全文件操作
            safe_file_operation(write_file, test_file, "安全写入的内容")
            
            # 验证文件是否存在且内容正确
            if os.path.exists(test_file):
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"✓ 安全文件操作测试通过")
                print(f"✓ 文件内容: {content}")
            else:
                print("✗ 文件未创建")
                
        except Exception as e:
            print(f"✗ 安全文件操作测试失败: {e}")

def test_process_pool_manager():
    """测试进程池管理器"""
    print("\n" + "=" * 50)
    print("测试进程池管理器")
    print("=" * 50)
    
    config = ConfigManager()
    
    try:
        with ProcessPoolManager(config) as pool_manager:
            pool_manager.create_pool(2)
            print("✓ 进程池创建成功")
            
            # 定义一个简单的测试函数
            def test_task(x):
                return x * x
            
            # 提交任务
            result = pool_manager.submit_task(test_task, 5)
            print("✓ 任务提交成功")
            
            # 等待完成
            pool_manager.wait_for_completion()
            print("✓ 任务执行完成")
            
            # 获取结果
            value = result.get()
            print(f"✓ 任务结果: {value}")
            
        print("进程池管理器测试通过")
        
    except Exception as e:
        print(f"✗ 进程池管理器测试失败: {e}")

def main():
    """主测试函数"""
    print("开始容灾机制测试")
    print("=" * 60)
    
    try:
        test_config_manager()
        test_resource_monitor()
        test_disaster_recovery()
        test_retry_mechanism()
        test_safe_file_operation()
        test_process_pool_manager()
        
        print("\n" + "=" * 60)
        print("✓ 所有容灾机制测试通过！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
