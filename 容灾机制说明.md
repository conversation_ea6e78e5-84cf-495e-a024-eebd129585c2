# Excel文件合并工具 - 容灾机制说明

## 概述

本工具已升级为具备完整容灾机制的增强版本，能够在各种异常情况下保护数据安全并支持任务恢复。

## 新增容灾功能

### 1. 配置管理系统 (ConfigManager)
- **自动配置文件生成**: 首次运行时自动创建 `disaster_config.ini` 配置文件
- **可配置参数**: 重试次数、延迟时间、资源阈值等均可配置
- **配置热加载**: 支持运行时重新加载配置

### 2. 资源监控系统 (ResourceMonitor)
- **内存监控**: 实时监控内存使用率，超过阈值时自动执行垃圾回收
- **磁盘空间检查**: 处理前检查磁盘空间，避免因空间不足导致的失败
- **后台监控**: 独立线程持续监控系统资源状态
- **智能建议**: 根据可用资源智能建议进程数量

### 3. 增强检查点系统 (DisasterRecovery)
- **任务状态跟踪**: 记录每个任务的详细执行状态和进度
- **文件完整性验证**: 使用MD5哈希验证生成文件的完整性
- **自动备份**: 检查点文件自动创建多个备份版本
- **断点续传**: 程序中断后可从上次停止的地方继续执行
- **数据恢复**: 检查点文件损坏时自动从备份恢复

### 4. 安全文件操作
- **原子写入**: 使用临时文件确保写入操作的原子性
- **自动备份**: 重要文件修改前自动创建备份
- **错误恢复**: 操作失败时自动从备份恢复
- **完整性检查**: 文件生成后验证大小和内容

### 5. 进程池管理 (ProcessPoolManager)
- **资源感知**: 根据系统资源动态调整进程数
- **异常处理**: 进程异常时自动重启和清理
- **优雅关闭**: 支持优雅关闭和强制终止
- **任务监控**: 实时监控任务执行状态

### 6. 增强重试机制
- **智能重试**: 区分可重试和不可重试的异常类型
- **指数退避**: 重试间隔采用指数退避算法
- **资源清理**: 重试前自动执行垃圾回收
- **详细日志**: 记录每次重试的详细信息

## 使用说明

### 基本使用
程序使用方式与原版本相同，但增加了以下功能：

1. **首次运行**: 自动创建配置文件和日志目录
2. **系统检查**: 启动时显示系统资源信息
3. **智能建议**: 根据系统配置建议最佳进程数
4. **进度监控**: 实时显示处理进度和资源使用情况
5. **断点续传**: 程序中断后重新运行可继续未完成的任务

### 配置文件说明
编辑 `disaster_config.ini` 文件可调整以下参数：

```ini
[DISASTER_RECOVERY]
max_retries = 3          # 最大重试次数
retry_delay = 2          # 重试延迟（秒）
checkpoint_interval = 10 # 检查点保存间隔

[RESOURCE_MONITORING]
memory_threshold = 80    # 内存使用率阈值（%）
disk_threshold = 90      # 磁盘使用率阈值（%）

[FILE_OPERATIONS]
verify_integrity = true  # 是否验证文件完整性
```

### 日志系统
- **自动日志**: 所有操作自动记录到 `logs/` 目录
- **分级日志**: 支持INFO、WARNING、ERROR等不同级别
- **时间戳**: 每个日志文件包含创建时间戳
- **详细信息**: 记录函数名、行号等详细调试信息

### 检查点文件
- **checkpoint.json**: 主检查点文件，记录所有任务状态
- **checkpoint.json.bak1-3**: 自动备份文件
- **自动清理**: 7天后自动清理过期的检查点数据

## 容灾场景处理

### 1. 程序异常中断
- **自动保存**: 处理过程中定期保存检查点
- **状态恢复**: 重启后自动跳过已完成的任务
- **文件保护**: 使用临时文件避免数据损坏

### 2. 系统资源不足
- **内存监控**: 内存不足时自动执行垃圾回收
- **磁盘检查**: 磁盘空间不足时提前终止并报警
- **进程调整**: 根据可用资源动态调整并发数

### 3. 文件操作失败
- **自动重试**: 文件操作失败时自动重试
- **备份恢复**: 操作失败时从备份恢复
- **完整性验证**: 确保生成的文件完整有效

### 4. 网络或存储故障
- **重试机制**: 自动重试失败的操作
- **错误隔离**: 单个文件失败不影响其他文件处理
- **详细日志**: 记录所有错误信息便于排查

## 性能优化

### 1. 内存管理
- **定期清理**: 自动执行垃圾回收释放内存
- **资源监控**: 实时监控内存使用情况
- **智能调度**: 根据内存使用情况调整处理策略

### 2. 并发控制
- **动态调整**: 根据系统负载动态调整进程数
- **资源感知**: 考虑CPU和内存限制
- **负载均衡**: 合理分配任务到各个进程

### 3. I/O优化
- **批量操作**: 减少频繁的文件I/O操作
- **缓存机制**: 合理使用内存缓存
- **异步处理**: 使用异步I/O提高效率

## 故障排除

### 常见问题
1. **内存不足**: 减少进程数或增加系统内存
2. **磁盘空间不足**: 清理磁盘空间或更换存储位置
3. **文件权限问题**: 确保程序有足够的文件读写权限
4. **配置文件错误**: 删除配置文件让程序重新生成

### 日志分析
- 查看 `logs/` 目录下的日志文件
- 关注ERROR和WARNING级别的日志
- 检查检查点文件的完整性

### 手动恢复
如需手动干预恢复：
1. 检查 `checkpoint.json` 文件内容
2. 删除损坏的检查点数据
3. 从备份文件恢复检查点
4. 重新运行程序

## 依赖要求

新增依赖包：
```
psutil>=5.8.0      # 系统资源监控
```

原有依赖保持不变：
```
openpyxl>=3.0.0    # Excel文件处理
```

## 版本信息

- **版本**: 2.0 增强容灾版
- **更新日期**: 2024年
- **兼容性**: 向下兼容原版本的所有功能
- **Python版本**: 3.7+
