2025-05-27 10:24:29,568 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:24:29,568 - INFO - load_checkpoint:222 - 成功加载检查点文件: checkpoint.json
2025-05-27 10:24:29,568 - INFO - main:1128 - ============================================================
2025-05-27 10:24:29,568 - INFO - main:1129 - Excel文件合并工具启动 - 增强容灾版本
2025-05-27 10:24:29,568 - INFO - main:1130 - ============================================================
2025-05-27 10:24:29,577 - INFO - main:1135 - 系统内存: 15.7GB (可用: 4.7GB)
2025-05-27 10:24:29,578 - INFO - main:1136 - 磁盘空间: 200.0GB (可用: 80.0GB)
2025-05-27 10:24:34,143 - INFO - main:1161 - 源路径: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分
2025-05-27 10:24:34,143 - INFO - main:1162 - 保存路径: C:\Users\<USER>\Desktop\新建文件夹 (2)\拆分
2025-05-27 10:24:34,144 - INFO - main:1165 - 正在加载数据字典...
2025-05-27 10:24:34,144 - INFO - dict_sj:622 - 开始加载数据字典文件: 工作簿12.xlsx
2025-05-27 10:24:34,208 - INFO - dict_sj:636 - 成功加载数据字典，共 819 条记录
2025-05-27 10:24:34,208 - INFO - dict_sj:641 - 测试数据访问成功: hz_zj_775 -> ('一般违规问题', '超医保支付范围', '成人使用小儿药品', '药品', '将成人使用小儿药品纳入医保结算。', '“小儿%”属于小儿药品。', 'A')
2025-05-27 10:24:34,208 - INFO - load_config:58 - 成功加载配置文件: disaster_config.ini
2025-05-27 10:24:34,209 - INFO - load_checkpoint:222 - 成功加载检查点文件: checkpoint.json
2025-05-27 10:24:34,218 - INFO - start_monitoring:196 - 资源监控已启动
2025-05-27 10:25:19,571 - ERROR - traverse_folders:1096 - 遍历文件夹时发生错误: invalid literal for int() with base 10: '& d:/python/python.exe c:/Users/<USER>/Desktop/py/hzhz.py'
2025-05-27 10:25:19,572 - ERROR - traverse_folders:1097 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1038, in traverse_folders
    process_count = int(input(f"请输入进程数 (建议: {suggested_processes}): ") or suggested_processes)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: invalid literal for int() with base 10: '& d:/python/python.exe c:/Users/<USER>/Desktop/py/hzhz.py'

2025-05-27 10:25:24,579 - INFO - stop_monitoring:203 - 资源监控已停止
2025-05-27 10:25:24,581 - INFO - traverse_folders:1105 - 容灾统计: 总任务 8, 完成 7, 完成率 87.5%
2025-05-27 10:25:24,582 - ERROR - main:1194 - 程序执行失败: invalid literal for int() with base 10: '& d:/python/python.exe c:/Users/<USER>/Desktop/py/hzhz.py'
2025-05-27 10:25:24,585 - ERROR - main:1195 - 错误详情: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1174, in main
    traverse_folders(source_path, save_path, data)
  File "c:\Users\<USER>\Desktop\py\hzhz.py", line 1038, in traverse_folders
    process_count = int(input(f"请输入进程数 (建议: {suggested_processes}): ") or suggested_processes)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: invalid literal for int() with base 10: '& d:/python/python.exe c:/Users/<USER>/Desktop/py/hzhz.py'

2025-05-27 10:25:24,585 - INFO - stop_monitoring:203 - 资源监控已停止
