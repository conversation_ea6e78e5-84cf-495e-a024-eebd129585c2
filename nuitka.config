# Nuitka配置文件
# 用于hzhz.py的构建配置

[nuitka]
# 基本设置
onefile = true
output-filename = hzhz.exe
output-dir = dist

# 优化设置
assume-yes-for-downloads = true
remove-output = true

# 插件设置
plugin-enable = multiprocessing

# Windows特定设置
windows-console-mode = force
mingw64 = true

# 包含设置
include-package = openpyxl
include-package = psutil
include-package = decimal
include-package = pathlib
include-package = json
include-package = configparser
include-package = hashlib
include-package = threading
include-package = multiprocessing
include-package = logging
include-package = traceback
include-package = datetime
include-package = time
include-package = shutil
include-package = os
include-package = sys
include-package = gc
include-package = signal

# 排除不需要的包
nofollow-import-to = tkinter
nofollow-import-to = matplotlib
nofollow-import-to = numpy
nofollow-import-to = pandas
nofollow-import-to = scipy
nofollow-import-to = PIL
nofollow-import-to = cv2

# 性能优化
jobs = 4
lto = no
